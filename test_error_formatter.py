#!/usr/bin/env python3
"""
Test script to verify the enhanced error message formatting functionality
"""

# Import from the main bot file instead of separate modules
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the integrated classes from the main bot file
from importlib import import_module
spec = import_module('import sqlite3')
ErrorFormatter = spec.ErrorFormatter
ValidationError = spec.ValidationError
format_role_error = spec.format_role_error
format_channel_error = spec.format_channel_error
format_multiple_errors = spec.format_multiple_errors

def test_role_error_formatting():
    """Test role error message formatting"""
    print("=== Testing Role Error Formatting ===")
    
    # Test role not found error
    error1 = ErrorFormatter.role_not_found_error("123456789", "team configuration", "Team Alpha")
    print("Role Not Found Error:")
    print(error1)
    print()
    
    # Test invalid role format error
    error2 = ErrorFormatter.role_invalid_format_error("invalid_id", "add team command")
    print("Invalid Role Format Error:")
    print(error2)
    print()

def test_channel_error_formatting():
    """Test channel error message formatting"""
    print("=== Testing Channel Error Formatting ===")
    
    # Test channel not configured error
    error1 = ErrorFormatter.channel_config_error("gametime", "Slot 1")
    print("Channel Not Configured Error:")
    print(error1)
    print()
    
    # Test channel not found error
    error2 = ErrorFormatter.channel_config_error("gametime", "Slot 1", "987654321")
    print("Channel Not Found Error:")
    print(error2)
    print()

def test_multiple_errors():
    """Test multiple error formatting"""
    print("=== Testing Multiple Error Formatting ===")
    
    errors = [
        ValidationError(
            error_type="not_found",
            item_type="role",
            item_id="123456789",
            item_name="Team Alpha",
            context="team configuration",
            suggestions=["Use /add_team to add the team role"]
        ),
        ValidationError(
            error_type="not_configured",
            item_type="channel",
            item_id="",
            item_name="Slot 1",
            context="gametime",
            suggestions=["Configure gametime channel in /setup"]
        ),
        ValidationError(
            error_type="invalid_format",
            item_type="role",
            item_id="bad_id",
            context="management role",
            suggestions=["Use proper Discord role ID format"]
        )
    ]
    
    multi_error = ErrorFormatter.multiple_validation_errors(errors, "roster command execution")
    print("Multiple Validation Errors:")
    print(multi_error)
    print()

def test_command_failure_error():
    """Test command failure error formatting"""
    print("=== Testing Command Failure Error ===")
    
    error = ErrorFormatter.command_failure_error(
        "roster",
        "Multiple role validation failures",
        ["Configure team roles with /add_team", "Set up management roles in /setup"]
    )
    print("Command Failure Error:")
    print(error)
    print()

def test_setup_guidance():
    """Test setup guidance generation"""
    print("=== Testing Setup Guidance ===")
    
    guidance = ErrorFormatter.setup_guidance_for_command("gametime", ["Gametime channel not configured", "Team roles missing"])
    print("Setup Guidance for Gametime Command:")
    print(guidance)
    print()

def test_utility_functions():
    """Test utility functions"""
    print("=== Testing Utility Functions ===")
    
    # Test format_role_error utility
    role_error = format_role_error("not_found", "123456789", "team setup", "Alpha Team")
    print("Utility Role Error:")
    print(role_error)
    print()
    
    # Test format_channel_error utility
    channel_error = format_channel_error("not_configured", {
        "type": "gametime",
        "slot_name": "Slot 1",
        "channel_id": None
    })
    print("Utility Channel Error:")
    print(channel_error)
    print()
    
    # Test format_multiple_errors utility
    error_dicts = [
        {
            "error_type": "not_found",
            "item_type": "role",
            "item_id": "123456789",
            "item_name": "Team Alpha",
            "context": "roster command"
        },
        {
            "error_type": "not_configured",
            "item_type": "channel",
            "item_id": "",
            "item_name": "Slot 1",
            "context": "gametime"
        }
    ]
    
    multi_error = format_multiple_errors(error_dicts, "command execution")
    print("Utility Multiple Errors:")
    print(multi_error)
    print()

if __name__ == "__main__":
    print("Testing Enhanced Error Message Formatting System")
    print("=" * 50)
    print()
    
    test_role_error_formatting()
    test_channel_error_formatting()
    test_multiple_errors()
    test_command_failure_error()
    test_setup_guidance()
    test_utility_functions()
    
    print("=" * 50)
    print("All tests completed successfully!")