#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ast

try:
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    ast.parse(content)
    print("✅ File syntax is completely valid!")
    
except SyntaxError as e:
    print(f"❌ Syntax Error: {e}")
    print(f"Line {e.lineno}: {e.text}")
    
except UnicodeDecodeError as e:
    print(f"❌ Unicode Error: {e}")
    
except Exception as e:
    print(f"❌ Other Error: {e}")
