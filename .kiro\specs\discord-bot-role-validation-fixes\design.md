# Design Document

## Overview

This design addresses role ID validation failures and gametime channel configuration issues in the Discord bot by implementing robust validation layers, graceful error handling, and improved user feedback mechanisms. The solution focuses on preventing crashes while providing clear guidance to users when configuration issues occur.

## Architecture

### Core Components

1. **Role Validation Layer**: Centralized role validation functions that handle all role ID checks
2. **Channel Validation Layer**: Enhanced channel configuration validation for gametime and other features  
3. **Error Handler System**: Standardized error messages and user guidance
4. **Database Cleanup Utilities**: Functions to detect and handle invalid stored role IDs

### Data Flow

```
Command Input → Role/Channel Validation → Database Query → Response Generation
     ↓                    ↓                     ↓              ↓
Error Handling ← Validation Results ← Data Validation ← User Feedback
```

## Components and Interfaces

### 1. Role Validation Module

**Purpose**: Centralize all role validation logic to ensure consistent handling across commands.

**Key Functions**:
- `validate_role_id(role_id: str, guild: discord.Guild) -> Tuple[discord.Role, str]`
- `safe_get_role(role_id: str, guild: discord.Guild) -> Optional[discord.Role]`
- `validate_team_role(role_id: str, guild_id: int) -> Dict[str, Any]`
- `cleanup_invalid_role_ids(guild_id: int) -> List[str]`

**Interface**:
```python
class RoleValidator:
    @staticmethod
    def validate_role_id(role_id: str, guild: discord.Guild) -> ValidationResult:
        """Validate a role ID and return role object or error details"""
        
    @staticmethod  
    def get_role_with_fallback(role_id: str, guild: discord.Guild, role_name: str = None) -> RoleResult:
        """Get role object with fallback display options"""
        
    @staticmethod
    def validate_management_roles(slot_id: str, guild: discord.Guild) -> Dict[str, RoleResult]:
        """Validate all management roles for a slot"""
```

### 2. Channel Validation Module

**Purpose**: Handle gametime channel and other channel configuration validation.

**Key Functions**:
- `validate_gametime_channel(slot_id: str, guild: discord.Guild) -> ChannelResult`
- `check_channel_permissions(channel: discord.Channel, bot_user: discord.Member) -> bool`
- `get_slot_channel_config(slot_id: str) -> Dict[str, str]`

**Interface**:
```python
class ChannelValidator:
    @staticmethod
    def validate_gametime_channel(slot_id: str, guild: discord.Guild) -> ChannelValidationResult:
        """Validate gametime channel exists and is accessible"""
        
    @staticmethod
    def get_channel_with_validation(channel_id: str, guild: discord.Guild) -> ChannelResult:
        """Get channel object with validation and error details"""
```

### 3. Enhanced Error Handler

**Purpose**: Provide consistent, helpful error messages across all commands.

**Key Functions**:
- `format_role_error(error_type: str, role_info: Dict) -> str`
- `format_channel_error(error_type: str, channel_info: Dict) -> str`
- `generate_setup_guidance(missing_items: List[str]) -> str`

**Interface**:
```python
class ErrorFormatter:
    @staticmethod
    def role_not_found_error(role_id: str, context: str) -> str:
        """Generate user-friendly role not found error"""
        
    @staticmethod
    def channel_config_error(channel_type: str, slot_name: str) -> str:
        """Generate channel configuration error with setup guidance"""
        
    @staticmethod
    def multiple_validation_errors(errors: List[ValidationError]) -> str:
        """Combine multiple validation errors into single message"""
```

## Data Models

### ValidationResult
```python
@dataclass
class ValidationResult:
    success: bool
    data: Optional[Any] = None
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    suggestions: List[str] = field(default_factory=list)
```

### RoleResult
```python
@dataclass  
class RoleResult:
    role: Optional[discord.Role]
    role_id: str
    is_valid: bool
    display_name: str
    error_message: Optional[str] = None
```

### ChannelValidationResult
```python
@dataclass
class ChannelValidationResult:
    channel: Optional[discord.TextChannel]
    channel_id: Optional[str]
    is_configured: bool
    is_accessible: bool
    error_message: Optional[str] = None
    setup_guidance: List[str] = field(default_factory=list)
```

## Error Handling

### Error Categories

1. **Role Not Found**: Role ID exists in database but not in Discord server
2. **Invalid Role ID**: Malformed role ID (non-numeric, too short)
3. **Permission Denied**: Role exists but user lacks access
4. **Channel Not Configured**: Channel ID not set in slot configuration
5. **Channel Inaccessible**: Channel exists but bot cannot access it

### Error Response Strategy

1. **Immediate Validation**: Check role/channel validity before processing commands
2. **Graceful Degradation**: Continue operation with limited functionality when possible
3. **Clear Messaging**: Provide specific error details and actionable solutions
4. **Logging**: Record validation failures for administrative review

### Fallback Mechanisms

- Display role names instead of mentions when role objects unavailable
- Show "Role ID: XXXXX (Not Found)" format for missing roles
- Provide setup command suggestions in error messages
- Continue displaying available information even when some roles are invalid

## Testing Strategy

### Unit Tests
- Role validation functions with various invalid inputs
- Channel validation with missing/inaccessible channels  
- Error message formatting consistency
- Database cleanup utility functions

### Integration Tests
- Full command execution with invalid role configurations
- Gametime command with various channel configuration states
- Multi-slot scenarios with mixed valid/invalid roles
- Permission checking with different user role combinations

### Error Scenario Tests
- Deleted roles that still exist in database
- Channels that bot cannot access
- Malformed role IDs in database
- Missing slot configurations
- Network/API failures during role/channel lookups

### User Experience Tests
- Error message clarity and helpfulness
- Setup guidance accuracy
- Command recovery after fixing configuration issues
- Performance impact of additional validation steps

## Implementation Notes

### Database Considerations
- Add validation flags to track role/channel validity status
- Implement soft deletion for invalid configurations
- Create audit log for automatic cleanup actions
- Consider caching validation results to reduce API calls

### Performance Optimization
- Batch role validation where possible
- Cache frequently accessed role/channel objects
- Implement validation result caching with TTL
- Minimize Discord API calls during validation

### Backward Compatibility
- Maintain existing command interfaces
- Preserve current database schema where possible
- Ensure existing configurations continue working
- Provide migration path for improved validation