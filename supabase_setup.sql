-- ===================================================
-- Supabase Database Migration Script
-- ===================================================
-- Run this script in the Supabase SQL Editor to:
-- 1. Delete all existing tables
-- 2. Create all required tables for your Discord bot
-- 3. Set up proper indexes for performance
--
-- Instructions:
-- 1. Go to your Supabase project
-- 2. Navigate to SQL Editor
-- 3. Copy and paste this entire script
-- 4. Click "Run" to execute
-- ===================================================

-- Drop all existing tables first (CASCADE removes dependencies)
DROP TABLE IF EXISTS live_management_lists CASCADE;
DROP TABLE IF EXISTS disband_transactions CASCADE;
DROP TABLE IF EXISTS gametimes CASCADE;
DROP TABLE IF EXISTS slot_team_blacklist CASCADE;
DROP TABLE IF EXISTS slot_team_whitelist CASCADE;
DROP TABLE IF EXISTS slot_trade_config CASCADE;
DROP TABLE IF EXISTS slot_verification_config CASCADE;
DROP TABLE IF EXISTS slot_demand_config CASCADE;
DROP TABLE IF EXISTS slot_command_settings CASCADE;
DROP TABLE IF EXISTS applications CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS contracts CASCADE;
DROP TABLE IF EXISTS league_teams CASCADE;
DROP TABLE IF EXISTS slot_configs CASCADE;
DROP TABLE IF EXISTS slots CASCADE;
DROP TABLE IF EXISTS guild_settings CASCADE;

-- Drop any existing indexes
DROP INDEX IF EXISTS idx_slots_guild_id;
DROP INDEX IF EXISTS idx_league_teams_guild_slot;
DROP INDEX IF EXISTS idx_contracts_guild_slot;
DROP INDEX IF EXISTS idx_contracts_status;
DROP INDEX IF EXISTS idx_transactions_guild_slot;
DROP INDEX IF EXISTS idx_gametimes_guild_slot;
DROP INDEX IF EXISTS idx_gametimes_status;

-- ===================================================
-- CREATE TABLES
-- ===================================================

-- Create guild_settings table (base server configuration)
CREATE TABLE guild_settings (
    guild_id TEXT PRIMARY KEY,
    admin_role TEXT,
    transaction_channel TEXT,
    log_channel TEXT,
    application_channel TEXT,
    suspended_role TEXT,
    suspended_channel TEXT,
    application_blacklist_role TEXT,
    announcement_channel TEXT,
    transaction_log_channel TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create slots table (league configurations per server)
CREATE TABLE slots (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_name TEXT NOT NULL,
    description TEXT,
    is_default INTEGER DEFAULT 0,
    weeks_per_season INTEGER DEFAULT 17,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_slots_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
);

-- Create slot_configs table (roles and channels per slot)
CREATE TABLE slot_configs (
    slot_id TEXT PRIMARY KEY,
    franchise_owner_role TEXT,
    general_manager_role TEXT,
    head_coach_role TEXT,
    assistant_coach_role TEXT,
    free_agent_role TEXT,
    transaction_channel TEXT,
    gametime_channel TEXT,
    referee_role TEXT,
    streamer_role TEXT,
    score_channel TEXT,
    trade_channel TEXT,
    draft_channel TEXT,
    pickup_host_role TEXT,
    pickup_channel TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_slot_configs_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create league_teams table (teams in each slot)
CREATE TABLE league_teams (
    guild_id BIGINT NOT NULL,
    role_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team_name TEXT NOT NULL,
    emoji TEXT NOT NULL DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (guild_id, role_id, slot_id),
    CONSTRAINT fk_league_teams_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create contracts table (player contracts)
CREATE TABLE contracts (
    contract_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    player_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    team_name TEXT,
    contract_amount INTEGER NOT NULL,
    contract_length INTEGER NOT NULL,
    contract_length_unit TEXT DEFAULT 'years',
    time_remaining INTEGER NOT NULL,
    time_remaining_unit TEXT DEFAULT 'years',
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    signed_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_date TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_contracts_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_contracts_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create transactions table (transaction history)
CREATE TABLE transactions (
    transaction_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT,
    transaction_type TEXT NOT NULL,
    description TEXT,
    performed_by TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    affected_users_count INTEGER DEFAULT 0,
    player_id TEXT,
    from_team_role TEXT,
    to_team_role TEXT,
    details TEXT,
    CONSTRAINT fk_transactions_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_transactions_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE SET NULL
);

-- Create applications table (team applications)
CREATE TABLE applications (
    application_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    application_type TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by TEXT,
    CONSTRAINT fk_applications_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_applications_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create slot_command_settings table (command toggles per slot)
CREATE TABLE slot_command_settings (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    sign_enabled TEXT DEFAULT 'on',
    release_enabled TEXT DEFAULT 'on',
    offer_enabled TEXT DEFAULT 'on',
    roster_cap INTEGER DEFAULT 25,
    money_cap_enabled TEXT DEFAULT 'on',
    money_cap_amount INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_slot_command_settings_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create slot_demand_config table (demand system configuration)
CREATE TABLE slot_demand_config (
    slot_id TEXT PRIMARY KEY,
    demands_enabled BOOLEAN DEFAULT false,
    demand_limit INTEGER DEFAULT 3,
    demand_channel TEXT,
    five_demands_role TEXT,
    four_demands_role TEXT,
    three_demands_role TEXT,
    two_demands_role TEXT,
    one_demand_role TEXT,
    no_demands_role TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_slot_demand_config_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create slot_verification_config table (verification system)
CREATE TABLE slot_verification_config (
    slot_id TEXT PRIMARY KEY,
    verification_enabled BOOLEAN DEFAULT false,
    verified_role TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_slot_verification_config_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create slot_trade_config table (trade system configuration)
CREATE TABLE slot_trade_config (
    slot_id TEXT PRIMARY KEY,
    trades_enabled BOOLEAN DEFAULT true,
    trade_channel TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_slot_trade_config_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create slot_team_whitelist table (allowed teams per slot)
CREATE TABLE slot_team_whitelist (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT uk_slot_team_whitelist UNIQUE(slot_id, team_role_id),
    CONSTRAINT fk_slot_team_whitelist_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create slot_team_blacklist table (restricted teams per slot)
CREATE TABLE slot_team_blacklist (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT uk_slot_team_blacklist UNIQUE(slot_id, team_role_id),
    CONSTRAINT fk_slot_team_blacklist_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create gametimes table (scheduled games)
CREATE TABLE gametimes (
    game_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team1_role_id TEXT NOT NULL,
    team1_name TEXT NOT NULL,
    team1_emoji TEXT,
    team2_role_id TEXT NOT NULL,
    team2_name TEXT NOT NULL,
    team2_emoji TEXT,
    scheduled_time BIGINT NOT NULL,
    timezone TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    channel_id TEXT NOT NULL,
    message_id TEXT NOT NULL,
    created_at BIGINT NOT NULL,
    created_by TEXT NOT NULL,
    referees TEXT DEFAULT '[]',
    streamer TEXT DEFAULT 'null',
    CONSTRAINT fk_gametimes_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_gametimes_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create disband_transactions table (team disbanding history)
CREATE TABLE disband_transactions (
    disband_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    team_name TEXT NOT NULL,
    disbanded_by TEXT NOT NULL,
    disbanded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    moved_to_fa_role TEXT,
    affected_users TEXT,
    CONSTRAINT fk_disband_transactions_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_disband_transactions_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create live_management_lists table (live updating lists)
CREATE TABLE live_management_lists (
    guild_id TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    display_type TEXT NOT NULL,
    auto_update TEXT DEFAULT 'yes',
    include_empty TEXT DEFAULT 'yes',
    league_style TEXT DEFAULT 'nfl',
    secondary_label TEXT DEFAULT 'AD',
    message_ids TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    active TEXT DEFAULT 'yes',
    PRIMARY KEY (guild_id, channel_id, slot_id),
    CONSTRAINT fk_live_management_lists_guild FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE,
    CONSTRAINT fk_live_management_lists_slot FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- ===================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ===================================================

-- Guild-based indexes
CREATE INDEX idx_slots_guild_id ON slots(guild_id);
CREATE INDEX idx_league_teams_guild_slot ON league_teams(guild_id, slot_id);
CREATE INDEX idx_contracts_guild_slot ON contracts(guild_id, slot_id);
CREATE INDEX idx_transactions_guild_slot ON transactions(guild_id, slot_id);
CREATE INDEX idx_gametimes_guild_slot ON gametimes(guild_id, slot_id);

-- Status-based indexes
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_gametimes_status ON gametimes(status);
CREATE INDEX idx_applications_status ON applications(status);

-- Player-based indexes
CREATE INDEX idx_contracts_player ON contracts(player_id);
CREATE INDEX idx_transactions_player ON transactions(player_id);

-- Team-based indexes
CREATE INDEX idx_contracts_team ON contracts(team_role_id);
CREATE INDEX idx_league_teams_role ON league_teams(role_id);

-- Time-based indexes
CREATE INDEX idx_contracts_created ON contracts(created_at);
CREATE INDEX idx_transactions_timestamp ON transactions(timestamp);
CREATE INDEX idx_gametimes_scheduled ON gametimes(scheduled_time);

-- ===================================================
-- CREATE ROW LEVEL SECURITY (RLS) POLICIES
-- ===================================================

-- Enable RLS on all tables for security
ALTER TABLE guild_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE league_teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_command_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_demand_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_verification_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_trade_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_team_whitelist ENABLE ROW LEVEL SECURITY;
ALTER TABLE slot_team_blacklist ENABLE ROW LEVEL SECURITY;
ALTER TABLE gametimes ENABLE ROW LEVEL SECURITY;
ALTER TABLE disband_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE live_management_lists ENABLE ROW LEVEL SECURITY;

-- Create policies that allow all operations for service role (your bot)
-- Note: Replace 'service_role' with your actual service role if different

-- Guild settings policies
CREATE POLICY "Enable all for service role" ON guild_settings FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON guild_settings FOR ALL USING (auth.role() = 'anon');

-- Slots policies
CREATE POLICY "Enable all for service role" ON slots FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slots FOR ALL USING (auth.role() = 'anon');

-- Slot configs policies
CREATE POLICY "Enable all for service role" ON slot_configs FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_configs FOR ALL USING (auth.role() = 'anon');

-- League teams policies
CREATE POLICY "Enable all for service role" ON league_teams FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON league_teams FOR ALL USING (auth.role() = 'anon');

-- Contracts policies
CREATE POLICY "Enable all for service role" ON contracts FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON contracts FOR ALL USING (auth.role() = 'anon');

-- Transactions policies
CREATE POLICY "Enable all for service role" ON transactions FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON transactions FOR ALL USING (auth.role() = 'anon');

-- Applications policies
CREATE POLICY "Enable all for service role" ON applications FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON applications FOR ALL USING (auth.role() = 'anon');

-- Slot command settings policies
CREATE POLICY "Enable all for service role" ON slot_command_settings FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_command_settings FOR ALL USING (auth.role() = 'anon');

-- Slot demand config policies
CREATE POLICY "Enable all for service role" ON slot_demand_config FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_demand_config FOR ALL USING (auth.role() = 'anon');

-- Slot verification config policies
CREATE POLICY "Enable all for service role" ON slot_verification_config FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_verification_config FOR ALL USING (auth.role() = 'anon');

-- Slot trade config policies
CREATE POLICY "Enable all for service role" ON slot_trade_config FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_trade_config FOR ALL USING (auth.role() = 'anon');

-- Slot team whitelist policies
CREATE POLICY "Enable all for service role" ON slot_team_whitelist FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_team_whitelist FOR ALL USING (auth.role() = 'anon');

-- Slot team blacklist policies
CREATE POLICY "Enable all for service role" ON slot_team_blacklist FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON slot_team_blacklist FOR ALL USING (auth.role() = 'anon');

-- Gametimes policies
CREATE POLICY "Enable all for service role" ON gametimes FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON gametimes FOR ALL USING (auth.role() = 'anon');

-- Disband transactions policies
CREATE POLICY "Enable all for service role" ON disband_transactions FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON disband_transactions FOR ALL USING (auth.role() = 'anon');

-- Live management lists policies
CREATE POLICY "Enable all for service role" ON live_management_lists FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Enable all for anon" ON live_management_lists FOR ALL USING (auth.role() = 'anon');

-- ===================================================
-- COMPLETION MESSAGE
-- ===================================================

-- Insert a test record to verify everything works
INSERT INTO guild_settings (guild_id, created_at) VALUES ('test_setup_complete', NOW());

-- Show completion message
SELECT 
    '✅ Database migration completed successfully!' as status,
    'All tables created with proper relationships and indexes' as details,
    NOW() as completed_at;

-- Show created tables
SELECT 
    table_name,
    'Created' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;
