#!/usr/bin/env python3
"""
Test script to verify the integrated validation system works correctly
"""
import sys
import os

# Add the current directory to the path to import our main file
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the components from our integrated file
try:
    from importlib.util import spec_from_file_location, module_from_spec
    
    # Load the main module
    spec = spec_from_file_location("main_bot", "import sqlite3.py")
    main_bot = module_from_spec(spec)
    spec.loader.exec_module(main_bot)
    
    # Test imports
    print("✅ Successfully imported main bot module")
    
    # Test validation classes
    role_validator = main_bot.RoleValidator()
    channel_validator = main_bot.ChannelValidator()
    error_formatter = main_bot.ErrorFormatter()
    validation_cache = main_bot.ValidationCache()
    
    print("✅ Successfully created validation class instances")
    
    # Test role ID format validation
    valid_role_id = "1234567890123456789"  # 19 digits
    invalid_role_id = "123"  # Too short
    
    assert main_bot.RoleValidator.validate_role_id_format_enhanced(valid_role_id) == True
    assert main_bot.RoleValidator.validate_role_id_format_enhanced(invalid_role_id) == False
    print("✅ Role ID format validation working correctly")
    
    # Test error formatting
    error_msg = main_bot.ErrorFormatter.role_not_found_error("123456789012345678", "test context")
    assert "Role Not Found" in error_msg
    assert "test context" in error_msg
    print("✅ Error formatting working correctly")
    
    # Test cache functionality
    cache = main_bot.ValidationCache()
    stats = cache.get_stats()
    assert isinstance(stats, dict)
    print("✅ Validation cache working correctly")
    
    # Test database cleanup result
    cleanup_result = main_bot.DatabaseCleanupResult()
    assert hasattr(cleanup_result, 'total_roles_checked')
    assert hasattr(cleanup_result, 'invalid_roles_found')
    print("✅ Database cleanup result structure working correctly")
    
    print("\n🎉 All integration tests passed successfully!")
    print("\n📋 Summary of integrated components:")
    print("   • Enhanced role validation with caching")
    print("   • Channel validation system")
    print("   • Comprehensive error formatting")
    print("   • Database cleanup utilities")
    print("   • Persistent gametime system")
    print("   • Thread-safe validation caching")
    
    print("\n🔧 Key improvements:")
    print("   • Fixed regex pattern syntax error")
    print("   • Consolidated 6 separate files into main file")
    print("   • Added performance caching with TTL")
    print("   • Enhanced error messages with context")
    print("   • Better exception handling")
    print("   • Automatic cache cleanup task")
    
except Exception as e:
    print(f"❌ Integration test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
