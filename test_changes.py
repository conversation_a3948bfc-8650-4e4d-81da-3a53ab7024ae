#!/usr/bin/env python3
"""
Test script to verify the changes made to the Discord bot:
1. Fixed /list_gametimes role mentions
2. Removed fallback channel behavior from /gametime
3. Added slot commands toggle system
"""

import sys
import os

# Add the current directory to path to import the bot file
sys.path.append(os.path.dirname(__file__))

def test_syntax():
    """Test that the file can be imported without syntax errors"""
    try:
        # Test compilation
        import py_compile
        py_compile.compile('import sqlite3.py', doraise=True)
        print("✅ Syntax check passed")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Syntax error: {e}")
        return False

def test_toggle_system():
    """Test that the toggle system is correctly implemented"""
    try:
        with open('import sqlite3.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if toggle variable exists
        if 'ENABLE_SLOT_COMMANDS = True' in content:
            print("✅ Toggle system variable found")
        else:
            print("❌ Toggle system variable not found")
            return False
            
        # Check if conditional statements exist for slot commands
        conditions = [
            'if ENABLE_SLOT_COMMANDS:',
            '@bot.tree.command(name="create_slot"',
            '@bot.tree.command(name="delete_slot"',
            '@bot.tree.command(name="list_slots"',
            '@bot.tree.command(name="set_default_slot"',
            '@bot.tree.command(name="reset_slot"'
        ]
        
        for condition in conditions:
            if condition in content:
                print(f"✅ Found: {condition}")
            else:
                print(f"❌ Missing: {condition}")
                return False
                
        return True
    except Exception as e:
        print(f"❌ Error checking toggle system: {e}")
        return False

def test_list_gametimes_fix():
    """Test that the list_gametimes fix is in place"""
    try:
        with open('import sqlite3.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the spacing fix in team display
        if 'team1_display = f"{team1_emoji or \'🏆\'} {team1_role.mention if team1_role else team1_name}"' in content:
            print("✅ list_gametimes role mention fix found")
            return True
        else:
            print("❌ list_gametimes role mention fix not found")
            return False
    except Exception as e:
        print(f"❌ Error checking list_gametimes fix: {e}")
        return False

def test_gametime_fallback_removal():
    """Test that the gametime fallback behavior was removed"""
    try:
        with open('import sqlite3.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that fallback behavior was removed
        if 'gametime_channel = interaction.channel' not in content:
            print("✅ Gametime fallback behavior removed")
        else:
            print("❌ Gametime fallback behavior still present")
            return False
            
        # Check that error message was added
        if 'No gametime channel is configured for this slot' in content:
            print("✅ Error message for missing gametime channel found")
            return True
        else:
            print("❌ Error message for missing gametime channel not found")
            return False
    except Exception as e:
        print(f"❌ Error checking gametime fallback removal: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Testing Discord bot changes...")
    print("=" * 50)
    
    tests = [
        ("Syntax Check", test_syntax),
        ("Toggle System", test_toggle_system),
        ("List Gametimes Fix", test_list_gametimes_fix),
        ("Gametime Fallback Removal", test_gametime_fallback_removal)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        if test_func():
            passed += 1
        
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Changes are working correctly.")
        print("\n📝 Summary of changes:")
        print("   • Fixed /list_gametimes to properly space role mentions")
        print("   • Removed fallback channel behavior from /gametime command")
        print("   • Added toggle system for slot commands to manage command limit")
        print("   • Set ENABLE_SLOT_COMMANDS = True by default")
    else:
        print("⚠️  Some tests failed. Please review the changes.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
