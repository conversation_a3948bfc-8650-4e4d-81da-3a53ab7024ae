#!/usr/bin/env python3
"""
Specific Command and Database Validation Test
Tests for potential runtime issues with Discord commands and database operations
"""

import sqlite3
import re
import json
from typing import List, Dict, Any

def test_database_schema_integrity():
    """Test database schema creation and table relationships"""
    print("🔍 Testing Database Schema Integrity...")
    
    # Create test database to validate schema
    test_db = sqlite3.connect(':memory:')
    cursor = test_db.cursor()
    
    # Essential tables that should exist
    essential_tables = [
        'guild_settings',
        'slots', 
        'league_teams',
        'slot_configs',
        'contracts',
        'slot_command_settings',
        'slot_demand_config',
        'disband_transactions',
        'live_management_lists'
    ]
    
    # Test each essential table creation
    print("📋 Testing essential table schemas...")
    
    schema_tests = {
        'guild_settings': """
            CREATE TABLE IF NOT EXISTS guild_settings (
                guild_id INTEGER PRIMARY KEY,
                admin_role INTEGER,
                log_channel INTEGER,
                application_channel INTEGER,
                suspended_role INTEGER,
                suspended_channel INTEGER,
                application_blacklist_role INTEGER,
                transaction_log_channel INTEGER
            )
        """,
        'slots': """
            CREATE TABLE IF NOT EXISTS slots (
                slot_id TEXT PRIMARY KEY,
                guild_id INTEGER,
                slot_name TEXT,
                description TEXT,
                is_default INTEGER DEFAULT 0,
                created_at TEXT
            )
        """,
        'league_teams': """
            CREATE TABLE IF NOT EXISTS league_teams (
                slot_id TEXT,
                team_name TEXT,
                role_id TEXT,
                emoji TEXT,
                guild_id INTEGER,
                PRIMARY KEY (slot_id, role_id)
            )
        """,
        'contracts': """
            CREATE TABLE IF NOT EXISTS contracts (
                player_id TEXT,
                team_role_id TEXT,
                slot_id TEXT,
                contract_amount INTEGER,
                contract_length INTEGER,
                time_unit TEXT,
                no_trade_clause INTEGER DEFAULT 0,
                signed_at TEXT,
                expires_at TEXT,
                guild_id INTEGER,
                performed_by TEXT,
                PRIMARY KEY (player_id, team_role_id, slot_id)
            )
        """
    }
    
    passed_schemas = 0
    for table_name, schema_sql in schema_tests.items():
        try:
            cursor.execute(schema_sql)
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if cursor.fetchone():
                print(f"   ✅ {table_name} schema valid")
                passed_schemas += 1
            else:
                print(f"   ❌ {table_name} schema failed")
        except Exception as e:
            print(f"   ❌ {table_name} schema error: {e}")
    
    test_db.close()
    
    print(f"📊 Schema Test Results: {passed_schemas}/{len(schema_tests)} passed")
    return passed_schemas == len(schema_tests)

def test_command_parameter_validation():
    """Test command parameters for potential issues"""
    print("🔍 Testing Command Parameter Validation...")
    
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all command definitions and their parameters
    command_pattern = r'@bot\.tree\.command\(name="([^"]*)"[^)]*\)\s*@app_commands\.describe\(([^)]*)\)\s*(?:@app_commands\.choices[^)]*\)\s*)*async def ([^(]*)\(([^)]*)\)'
    commands = re.findall(command_pattern, content, re.DOTALL)
    
    issues_found = []
    commands_tested = 0
    
    for cmd_name, describe_params, func_name, func_params in commands:
        commands_tested += 1
        
        # Check if function name matches command name convention
        expected_func_name = cmd_name.replace('-', '_').replace(' ', '_')
        if func_name != expected_func_name and func_name != cmd_name:
            # Some flexibility for function naming
            pass
        
        # Check for interaction parameter (required for all slash commands)
        if 'interaction: discord.Interaction' not in func_params:
            issues_found.append(f"Command '{cmd_name}' missing interaction parameter")
        
        # Check for proper async definition
        # This is checked in the regex pattern by looking for 'async def'
        
        print(f"   ✅ {cmd_name} - parameters valid")
    
    print(f"📊 Command Parameter Test Results: {commands_tested} commands tested, {len(issues_found)} issues")
    
    if issues_found:
        for issue in issues_found:
            print(f"   ⚠️ {issue}")
    
    return len(issues_found) == 0

def test_database_operations():
    """Test database operations for potential SQL injection and errors"""
    print("🔍 Testing Database Operations...")
    
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find SQL operations
    sql_operations = re.findall(r'(cursor\.execute|bot\.db\.execute|\.execute)\s*\(\s*["\']([^"\']*)["\']', content)
    
    issues_found = []
    parameterized_queries = 0
    total_queries = len(sql_operations)
    
    for operation, query in sql_operations:
        # Check for parameterized queries (good practice)
        if '?' in query or '%s' in query:
            parameterized_queries += 1
        
        # Check for potential SQL injection vulnerabilities
        if any(dangerous_pattern in query.lower() for dangerous_pattern in ['drop table', 'truncate', 'delete from', 'update'] if dangerous_pattern in query.lower() and '?' not in query):
            issues_found.append(f"Potentially unsafe SQL: {query[:50]}...")
    
    print(f"📊 Database Operations Test Results:")
    print(f"   • Total SQL queries found: {total_queries}")
    print(f"   • Parameterized queries: {parameterized_queries}")
    print(f"   • Safety issues: {len(issues_found)}")
    
    if issues_found:
        for issue in issues_found:
            print(f"   ⚠️ {issue}")
    else:
        print("   ✅ No SQL safety issues detected")
    
    return len(issues_found) == 0

def test_error_handling_coverage():
    """Test error handling coverage in critical functions"""
    print("🔍 Testing Error Handling Coverage...")
    
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find async functions (command handlers)
    async_functions = re.findall(r'async def ([^(]*)\([^)]*interaction[^)]*\):', content)
    
    # Check each command function for try/except blocks
    functions_with_error_handling = 0
    total_command_functions = len(async_functions)
    
    for func_name in async_functions:
        # Find the function body and check for try/except
        func_pattern = rf'async def {re.escape(func_name)}\([^)]*\):.*?(?=(?:async def|\Z))'
        func_match = re.search(func_pattern, content, re.DOTALL)
        
        if func_match:
            func_body = func_match.group(0)
            if 'try:' in func_body and 'except' in func_body:
                functions_with_error_handling += 1
                print(f"   ✅ {func_name} - has error handling")
            else:
                print(f"   ⚠️ {func_name} - no error handling detected")
    
    coverage_percentage = (functions_with_error_handling / total_command_functions * 100) if total_command_functions > 0 else 0
    
    print(f"📊 Error Handling Coverage: {functions_with_error_handling}/{total_command_functions} ({coverage_percentage:.1f}%)")
    
    return coverage_percentage >= 70  # 70% coverage is acceptable

def test_discord_interaction_patterns():
    """Test for proper Discord interaction patterns"""
    print("🔍 Testing Discord Interaction Patterns...")
    
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues_found = []
    
    # Check for proper response patterns
    response_patterns = [
        ('interaction.response.defer', 'defer responses'),
        ('interaction.followup.send', 'followup messages'),
        ('interaction.response.send_message', 'direct responses'),
        ('ephemeral=True', 'ephemeral responses')
    ]
    
    pattern_counts = {}
    for pattern, description in response_patterns:
        count = len(re.findall(re.escape(pattern), content))
        pattern_counts[description] = count
        print(f"   • {description}: {count} instances")
    
    # Check for potential double-response issues
    defer_count = pattern_counts.get('defer responses', 0)
    direct_response_count = pattern_counts.get('direct responses', 0)
    followup_count = pattern_counts.get('followup messages', 0)
    
    if defer_count > 0 and followup_count > 0:
        print("   ✅ Proper defer/followup pattern detected")
    
    if direct_response_count > 0:
        print("   ✅ Direct response pattern detected")
    
    # Check for view persistence
    if 'bot.add_view' in content:
        print("   ✅ View persistence implemented")
    
    print(f"📊 Discord Interaction Test Results: {len(issues_found)} issues found")
    
    return len(issues_found) == 0

def run_deployment_validation():
    """Run all deployment validation tests"""
    print("🚀 Running Deployment Validation Tests")
    print("=" * 60)
    
    tests = [
        ("Database Schema Integrity", test_database_schema_integrity),
        ("Command Parameter Validation", test_command_parameter_validation),
        ("Database Operations Safety", test_database_operations),
        ("Error Handling Coverage", test_error_handling_coverage),
        ("Discord Interaction Patterns", test_discord_interaction_patterns)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} PASSED")
                passed_tests += 1
            else:
                print(f"⚠️ {test_name} NEEDS ATTENTION")
        except Exception as e:
            print(f"❌ {test_name} FAILED: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 DEPLOYMENT VALIDATION SUMMARY")
    print("=" * 60)
    
    pass_rate = (passed_tests / total_tests) * 100
    
    print(f"Tests Passed: {passed_tests}/{total_tests} ({pass_rate:.1f}%)")
    
    if pass_rate >= 90:
        print("🟢 EXCELLENT: Bot is highly optimized for deployment!")
    elif pass_rate >= 75:
        print("🟡 GOOD: Bot is ready for deployment with minor optimizations")
    elif pass_rate >= 60:
        print("🟠 ACCEPTABLE: Bot can be deployed but improvements recommended")
    else:
        print("🔴 NEEDS WORK: Consider addressing issues before deployment")
    
    return pass_rate >= 75

if __name__ == "__main__":
    is_ready = run_deployment_validation()
    
    if is_ready:
        print("\n🚀 DEPLOYMENT READY: Your RosterFlow bot is optimized for PebbleHost!")
        print("\n📋 Pre-deployment checklist:")
        print("   ✅ Install discord.py on your server")
        print("   ✅ Set up your bot token as an environment variable")
        print("   ✅ Ensure SQLite is available (usually pre-installed)")
        print("   ✅ Upload your bot file to the server")
        print("   ✅ Test with a small Discord server first")
    else:
        print("\n⚠️ Consider addressing the identified issues for optimal performance.")
