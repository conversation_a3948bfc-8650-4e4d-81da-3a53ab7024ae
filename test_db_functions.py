#!/usr/bin/env python3
"""
Unit tests for database functions in the Discord bot
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the module to test
import importlib.util
spec = importlib.util.spec_from_file_location("bot_module", "import sqlite3.py")
bot_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(bot_module)

class TestDatabaseConstants(unittest.TestCase):
    """Test the database constants"""
    
    def test_db_constants(self):
        """Test that database constants are defined correctly"""
        self.assertEqual(bot_module.DB, "transaction_bot.db")
        self.assertEqual(bot_module.TEAM_TABLE, "league_teams")
        self.assertEqual(bot_module.SLOT_TABLE, "slots")
        self.assertEqual(bot_module.SLOT_CONFIGS_TABLE, "slot_configs")

class TestGetDb(unittest.TestCase):
    """Test the get_db function"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a mock for bot.db
        self.original_db = bot_module.bot.db
        bot_module.bot.db = Mock()
    
    def tearDown(self):
        """Tear down test fixtures"""
        # Restore original function
        bot_module.bot.db = self.original_db
    
    def test_get_db(self):
        """Test that get_db returns bot.db"""
        result = bot_module.get_db()
        self.assertEqual(result, bot_module.bot.db)

class TestFootballEmojis(unittest.TestCase):
    """Test the FOOTBALL_EMOJIS set"""
    
    def test_football_emojis(self):
        """Test that FOOTBALL_EMOJIS contains expected emojis"""
        self.assertIn("🏈", bot_module.FOOTBALL_EMOJIS)
        self.assertIn("🏉", bot_module.FOOTBALL_EMOJIS)
        self.assertIn("🦶", bot_module.FOOTBALL_EMOJIS)
        self.assertEqual(len(bot_module.FOOTBALL_EMOJIS), 3)

if __name__ == "__main__":
    unittest.main()