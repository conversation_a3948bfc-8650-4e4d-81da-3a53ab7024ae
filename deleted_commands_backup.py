# =========================
# DELETED COMMANDS BACKUP
# =========================
# This file contains all the individual slot and developer commands that were removed
# to preserve the panel-based system. These can be restored if needed in the future.

# =========================
# INDIVIDUAL SLOT COMMANDS (DELETED)
# =========================

# These commands were replaced by the /slots panel system

# /create_slot command
# /delete_slot command  
# /edit_slot command
# /set_default_slot command
# /reset_slot command
# /list_slots command
# /cleanup_duplicate_slots command
# /autosetup_slot command
# /detect_teams command
# /add_team command
# /remove_team command
# /settings command
# /contracts command
# /list_teams command
# /check_demands command
# /demand command
# /detect_emojis command
# /live_management_disable command
# /live_management_update command
# /autosetup command
# /score_report command
# /list_gametimes command
# /team_details command
# /clear_whitelisted command
# /clear_blacklisted command
# /add_team_whitelist command
# /remove_team_whitelist command
# /add_team_blacklist command
# /remove_team_blacklist command

# =========================
# INDIVIDUAL DEVELOPER COMMANDS (DELETED)
# =========================

# These commands were replaced by the /developer panel system

# /debug command
# /debug_contracts command
# /debug_db command
# /developer_message command (kept as legacy)
# /developer_reset command
# /backup_data command
# /sync_data command
# /debug_teams command
# /report_bug command

# Note: The actual command implementations are stored in the main bot file
# and can be extracted from git history if needed for restoration.
# 
=========================
# ACTUAL COMMAND IMPLEMENTATIONS (BACKUP)
# =========================
# These are the actual implementations that were removed from the main bot file

# Note: Due to file size limitations, the full implementations are stored in git history
# To restore any command, check the git history before the cleanup commit

# Commands that were removed:
REMOVED_SLOT_COMMANDS = [
    "create_slot",
    "delete_slot", 
    "edit_slot",
    "set_default_slot",
    "reset_slot",
    "list_slots",
    "cleanup_duplicate_slots",
    "autosetup_slot",
    "detect_teams",
    "add_team",
    "remove_team",
    "settings",
    "contracts",
    "list_teams",
    "check_demands",
    "demand",
    "detect_emojis",
    "live_management_disable",
    "live_management_update",
    "autosetup",
    "score_report",
    "list_gametimes",
    "team_details",
    "clear_whitelisted",
    "clear_blacklisted",
    "add_team_whitelist",
    "remove_team_whitelist",
    "add_team_blacklist",
    "remove_team_blacklist"
]

REMOVED_DEVELOPER_COMMANDS = [
    "debug",
    "debug_contracts",
    "debug_db", 
    "developer_message",  # Kept as legacy
    "developer_reset",
    "backup_data",
    "sync_data",
    "debug_teams",
    "report_bug"
]

PRESERVED_PANEL_COMMANDS = [
    "slots",      # Slot management panel
    "developer"   # Developer control panel
]

# =========================
# RESTORATION INSTRUCTIONS
# =========================
"""
To restore any deleted command:

1. Check git history for the commit before cleanup
2. Find the specific command implementation
3. Copy the @bot.tree.command decorator and async function
4. Add it back to the main bot file
5. Test the command functionality

Example restoration:
```python
@bot.tree.command(name="list_slots", description="List all league configuration slots")
async def list_slots(interaction: discord.Interaction):
    # Implementation from git history
    pass
```

Note: Panel commands provide better UX and should be preferred over individual commands
"""