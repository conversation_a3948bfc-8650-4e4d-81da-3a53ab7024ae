#!/usr/bin/env python3
"""
Fix Syntax Errors from Automatic Error Handling Insertion
"""

import re

def fix_syntax_errors():
    """Fix syntax errors caused by misplaced error handling"""
    print("🔧 Fixing syntax errors from automatic error handling...")
    
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove misplaced error handling blocks that are outside functions
    # Pattern: @bot.tree.command or similar decorators followed by try/except blocks
    
    # First, find and remove standalone except blocks that are not part of a try block
    lines = content.split('\n')
    cleaned_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this is a misplaced except block
        if (line.strip().startswith('except Exception as error:') and 
            i > 0 and 
            not any('try:' in lines[j] for j in range(max(0, i-10), i))):
            
            # Skip this except block and its contents
            print(f"   Removing misplaced except block at line {i+1}")
            i += 1
            # Skip the except block contents
            while i < len(lines) and (lines[i].startswith('        ') or lines[i].strip() == ''):
                i += 1
            continue
        
        # Check if this is a try block that's incorrectly placed (not inside a function)
        elif (line.strip() == 'try:' and 
              i > 0 and 
              not any(lines[j].strip().startswith('async def ') or lines[j].strip().startswith('def ') 
                     for j in range(max(0, i-5), i))):
            
            # This is likely a misplaced try block, remove it
            print(f"   Removing misplaced try block at line {i+1}")
            i += 1
            # Skip until we find a decorator or function definition
            while i < len(lines) and not (lines[i].strip().startswith('@') or 
                                         lines[i].strip().startswith('async def ') or
                                         lines[i].strip().startswith('def ') or
                                         lines[i].strip().startswith('class ')):
                if lines[i].strip().startswith('except'):
                    # Skip the entire except block too
                    while i < len(lines) and (lines[i].startswith('        ') or 
                                            lines[i].strip().startswith('except') or
                                            lines[i].strip() == '' or
                                            'Error in' in lines[i] or
                                            'interaction.response' in lines[i] or
                                            'interaction.followup' in lines[i]):
                        i += 1
                    break
                i += 1
            continue
        
        cleaned_lines.append(line)
        i += 1
    
    # Write the cleaned content
    cleaned_content = '\n'.join(cleaned_lines)
    
    with open('import sqlite3.py', 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print("   ✅ Fixed syntax errors from misplaced error handling")
    return True

def verify_syntax():
    """Verify that the syntax is now correct"""
    print("🔍 Verifying syntax...")
    
    import subprocess
    import sys
    
    try:
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'import sqlite3.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ Syntax verification passed!")
            return True
        else:
            print(f"   ❌ Syntax errors still present:")
            print(f"   {result.stderr}")
            return False
    except Exception as e:
        print(f"   ⚠️ Could not verify syntax: {e}")
        return False

def main():
    """Run syntax fixes"""
    print("🚀 Fixing Syntax Errors")
    print("=" * 40)
    
    success = fix_syntax_errors()
    
    if success:
        if verify_syntax():
            print("\n🎉 All syntax errors fixed! Bot is ready for deployment!")
        else:
            print("\n⚠️ Some syntax issues may remain. Check the error output above.")
    else:
        print("\n❌ Could not fix syntax errors automatically.")

if __name__ == "__main__":
    main()
