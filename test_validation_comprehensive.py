#!/usr/bin/env python3
"""
Comprehensive test suite for role and channel validation functions
"""

import unittest
from unittest.mock import Mock, Magic<PERSON>ock, patch
import discord
from datetime import datetime
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import from the main bot file instead of separate modules
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the integrated classes from the main bot file
from importlib import import_module
spec = import_module('import sqlite3')
RoleValidator = spec.RoleValidator
RoleValidationResult = spec.RoleValidationResult
ChannelValidator = spec.ChannelValidator
ChannelValidationResult = spec.ChannelValidationResult
ErrorFormatter = spec.ErrorFormatter
ValidationError = spec.ValidationError
ValidationCache = spec.ValidationCache
DatabaseRoleCleanup = spec.DatabaseRoleCleanup

class TestRoleValidation(unittest.TestCase):
    """Test role validation functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 123456789
        self.mock_guild.name = "Test Guild"
        
        self.mock_role = Mock(spec=discord.Role)
        self.mock_role.id = 987654321
        self.mock_role.name = "Test Role"
        
        self.mock_guild.get_role.return_value = self.mock_role
    
    def test_validate_role_id_format_valid(self):
        """Test role ID format validation with valid IDs"""
        valid_ids = [
            "123456789012345678",  # 18 digits
            "12345678901234567",   # 17 digits
            "1234567890123456789"  # 19 digits
        ]
        
        for role_id in valid_ids:
            with self.subTest(role_id=role_id):
                self.assertTrue(RoleValidator.validate_role_id_format(role_id))
    
    def test_validate_role_id_format_invalid(self):
        """Test role ID format validation with invalid IDs"""
        invalid_ids = [
            "123456789012345",     # Too short
            "12345678901234567890", # Too long
            "abc123456789012345",   # Contains letters
            "123-456-789012345",    # Contains hyphens
            "",                     # Empty string
            None,                   # None value
            "123 456 789012345"     # Contains spaces
        ]
        
        for role_id in invalid_ids:
            with self.subTest(role_id=role_id):
                self.assertFalse(RoleValidator.validate_role_id_format(role_id))
    
    def test_safe_get_role_valid(self):
        """Test safe role retrieval with valid role"""
        role_id = "987654321"
        result = RoleValidator.safe_get_role(role_id, self.mock_guild)
        
        self.assertEqual(result, self.mock_role)
        self.mock_guild.get_role.assert_called_once_with(987654321)
    
    def test_safe_get_role_not_found(self):
        """Test safe role retrieval when role not found"""
        self.mock_guild.get_role.return_value = None
        
        role_id = "987654321"
        result = RoleValidator.safe_get_role(role_id, self.mock_guild)
        
        self.assertIsNone(result)
    
    def test_safe_get_role_invalid_format(self):
        """Test safe role retrieval with invalid format"""
        role_id = "invalid_id"
        result = RoleValidator.safe_get_role(role_id, self.mock_guild)
        
        self.assertIsNone(result)
        self.mock_guild.get_role.assert_not_called()
    
    def test_validate_role_id_success(self):
        """Test comprehensive role validation with valid role"""
        role_id = "987654321"
        result = RoleValidator.validate_role_id(role_id, self.mock_guild, "test context")
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.role, self.mock_role)
        self.assertEqual(result.role_id, role_id)
        self.assertEqual(result.display_name, "Test Role")
        self.assertIsNone(result.error_type)
    
    def test_validate_role_id_not_found(self):
        """Test comprehensive role validation when role not found"""
        self.mock_guild.get_role.return_value = None
        
        role_id = "987654321"
        result = RoleValidator.validate_role_id(role_id, self.mock_guild, "test context")
        
        self.assertFalse(result.is_valid)
        self.assertIsNone(result.role)
        self.assertEqual(result.role_id, role_id)
        self.assertEqual(result.error_type, "not_found")
        self.assertIn("not found", result.error_message.lower())
    
    def test_validate_role_id_invalid_format(self):
        """Test comprehensive role validation with invalid format"""
        role_id = "invalid_id"
        result = RoleValidator.validate_role_id(role_id, self.mock_guild, "test context")
        
        self.assertFalse(result.is_valid)
        self.assertIsNone(result.role)
        self.assertEqual(result.role_id, role_id)
        self.assertEqual(result.error_type, "invalid_format")
        self.assertIn("invalid", result.error_message.lower())
    
    def test_validate_multiple_roles(self):
        """Test validation of multiple roles"""
        role_ids = ["987654321", "123456789"]
        
        # Mock different responses for different roles
        def mock_get_role(role_id):
            if role_id == 987654321:
                return self.mock_role
            else:
                return None
        
        self.mock_guild.get_role.side_effect = mock_get_role
        
        results = RoleValidator.validate_multiple_roles(role_ids, self.mock_guild, "test context")
        
        self.assertEqual(len(results), 2)
        self.assertTrue(results["987654321"].is_valid)
        self.assertFalse(results["123456789"].is_valid)
    
    def test_validate_team_role(self):
        """Test team role validation with specific context"""
        role_id = "987654321"
        team_name = "Test Team"
        
        result = RoleValidator.validate_team_role(role_id, self.mock_guild, team_name)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.role, self.mock_role)
        # Should have team-specific suggestions when invalid
    
    def test_validate_management_roles(self):
        """Test management roles validation"""
        role_configs = {
            'franchise_owner_role': '987654321',
            'general_manager_role': '123456789',
            'head_coach_role': '',  # Empty role
            'assistant_coach_role': 'invalid_id'  # Invalid format
        }
        
        # Mock different responses
        def mock_get_role(role_id):
            if role_id == 987654321:
                return self.mock_role
            else:
                return None
        
        self.mock_guild.get_role.side_effect = mock_get_role
        
        results = RoleValidator.validate_management_roles("slot_1", self.mock_guild, role_configs)
        
        self.assertEqual(len(results), 4)
        self.assertTrue(results['franchise_owner_role'].is_valid)
        self.assertFalse(results['general_manager_role'].is_valid)
        self.assertFalse(results['head_coach_role'].is_valid)  # Not configured
        self.assertFalse(results['assistant_coach_role'].is_valid)  # Invalid format

class TestChannelValidation(unittest.TestCase):
    """Test channel validation functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 123456789
        self.mock_guild.name = "Test Guild"
        
        self.mock_channel = Mock(spec=discord.TextChannel)
        self.mock_channel.id = 987654321
        self.mock_channel.name = "test-channel"
        
        self.mock_guild.get_channel.return_value = self.mock_channel
        
        self.mock_db = Mock()
    
    def test_validate_channel_id_format_valid(self):
        """Test channel ID format validation with valid IDs"""
        valid_ids = [
            "123456789012345678",  # 18 digits
            "12345678901234567",   # 17 digits
            "1234567890123456789"  # 19 digits
        ]
        
        for channel_id in valid_ids:
            with self.subTest(channel_id=channel_id):
                self.assertTrue(ChannelValidator.validate_channel_id_format(channel_id))
    
    def test_validate_channel_id_format_invalid(self):
        """Test channel ID format validation with invalid IDs"""
        invalid_ids = [
            "123456789012345",     # Too short
            "12345678901234567890", # Too long
            "abc123456789012345",   # Contains letters
            "",                     # Empty string
            None                    # None value
        ]
        
        for channel_id in invalid_ids:
            with self.subTest(channel_id=channel_id):
                self.assertFalse(ChannelValidator.validate_channel_id_format(channel_id))
    
    def test_safe_get_channel_valid(self):
        """Test safe channel retrieval with valid channel"""
        channel_id = "987654321"
        result = ChannelValidator.safe_get_channel(channel_id, self.mock_guild)
        
        self.assertEqual(result, self.mock_channel)
        self.mock_guild.get_channel.assert_called_once_with(987654321)
    
    def test_safe_get_channel_not_found(self):
        """Test safe channel retrieval when channel not found"""
        self.mock_guild.get_channel.return_value = None
        
        channel_id = "987654321"
        result = ChannelValidator.safe_get_channel(channel_id, self.mock_guild)
        
        self.assertIsNone(result)
    
    def test_safe_get_channel_wrong_type(self):
        """Test safe channel retrieval with wrong channel type"""
        mock_voice_channel = Mock(spec=discord.VoiceChannel)
        self.mock_guild.get_channel.return_value = mock_voice_channel
        
        channel_id = "987654321"
        result = ChannelValidator.safe_get_channel(channel_id, self.mock_guild)
        
        self.assertIsNone(result)  # Should return None for non-text channels
    
    def test_validate_gametime_channel_success(self):
        """Test gametime channel validation with valid configuration"""
        # Mock database response
        self.mock_db.execute.return_value = [("987654321",)]
        
        result = ChannelValidator.validate_gametime_channel("slot_1", self.mock_guild, self.mock_db, "Test Slot")
        
        self.assertTrue(result.is_configured)
        self.assertTrue(result.is_accessible)
        self.assertEqual(result.channel, self.mock_channel)
        self.assertEqual(result.channel_id, "987654321")
    
    def test_validate_gametime_channel_not_configured(self):
        """Test gametime channel validation when not configured"""
        # Mock database response - no configuration
        self.mock_db.execute.return_value = []
        
        result = ChannelValidator.validate_gametime_channel("slot_1", self.mock_guild, self.mock_db, "Test Slot")
        
        self.assertFalse(result.is_configured)
        self.assertFalse(result.is_accessible)
        self.assertEqual(result.error_type, "not_configured")
        self.assertIn("not configured", result.error_message.lower())
    
    def test_validate_gametime_channel_not_found(self):
        """Test gametime channel validation when channel not found"""
        # Mock database response with channel ID
        self.mock_db.execute.return_value = [("987654321",)]
        # Mock guild response - channel not found
        self.mock_guild.get_channel.return_value = None
        
        result = ChannelValidator.validate_gametime_channel("slot_1", self.mock_guild, self.mock_db, "Test Slot")
        
        self.assertTrue(result.is_configured)
        self.assertFalse(result.is_accessible)
        self.assertEqual(result.error_type, "not_found")
        self.assertIn("not found", result.error_message.lower())
    
    def test_check_channel_permissions(self):
        """Test channel permission checking"""
        mock_bot_member = Mock(spec=discord.Member)
        mock_permissions = Mock()
        mock_permissions.view_channel = True
        mock_permissions.send_messages = True
        mock_permissions.embed_links = False
        mock_permissions.read_message_history = True
        
        self.mock_channel.permissions_for.return_value = mock_permissions
        
        permissions = ChannelValidator.check_channel_permissions(self.mock_channel, mock_bot_member)
        
        self.assertTrue(permissions['view_channel'])
        self.assertTrue(permissions['send_messages'])
        self.assertFalse(permissions['embed_links'])
        self.assertTrue(permissions['read_message_history'])

class TestErrorFormatter(unittest.TestCase):
    """Test error message formatting"""
    
    def test_role_not_found_error(self):
        """Test role not found error formatting"""
        error_msg = ErrorFormatter.role_not_found_error("123456789", "test context", "Test Role")
        
        self.assertIn("Role Not Found", error_msg)
        self.assertIn("Test Role", error_msg)
        self.assertIn("test context", error_msg)
        self.assertIn("How to fix this", error_msg)
    
    def test_role_invalid_format_error(self):
        """Test role invalid format error formatting"""
        error_msg = ErrorFormatter.role_invalid_format_error("invalid_id", "test context")
        
        self.assertIn("Invalid Role ID", error_msg)
        self.assertIn("invalid_id", error_msg)
        self.assertIn("test context", error_msg)
        self.assertIn("17-19 digit numbers", error_msg)
    
    def test_channel_config_error(self):
        """Test channel configuration error formatting"""
        error_msg = ErrorFormatter.channel_config_error("gametime", "Test Slot")
        
        self.assertIn("Channel Not Configured", error_msg)
        self.assertIn("gametime", error_msg)
        self.assertIn("Test Slot", error_msg)
        self.assertIn("How to fix this", error_msg)
    
    def test_multiple_validation_errors(self):
        """Test multiple error formatting"""
        errors = [
            ValidationError(
                error_type="not_found",
                item_type="role",
                item_id="123456789",
                item_name="Test Role",
                context="test context"
            ),
            ValidationError(
                error_type="not_configured",
                item_type="channel",
                item_id="",
                item_name="Gametime Channel",
                context="gametime"
            )
        ]
        
        error_msg = ErrorFormatter.multiple_validation_errors(errors, "test command")
        
        self.assertIn("Multiple Configuration Issues", error_msg)
        self.assertIn("Role Issues", error_msg)
        self.assertIn("Channel Issues", error_msg)
        self.assertIn("Test Role", error_msg)
        self.assertIn("Gametime Channel", error_msg)

class TestValidationCache(unittest.TestCase):
    """Test validation result caching"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.cache = ValidationCache()
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 123456789
    
    def test_role_cache_miss(self):
        """Test role cache miss"""
        result = self.cache.get_role_validation(123456789, "987654321", "test")
        self.assertIsNone(result)
        
        stats = self.cache.get_stats()
        self.assertEqual(stats['role_misses'], 1)
        self.assertEqual(stats['role_hits'], 0)
    
    def test_role_cache_hit(self):
        """Test role cache hit"""
        # Create a mock validation result
        mock_result = RoleValidationResult(
            role_id="987654321",
            is_valid=True,
            display_name="Test Role"
        )
        
        # Cache the result
        self.cache.cache_role_validation(123456789, "987654321", mock_result, "test")
        
        # Retrieve from cache
        cached_result = self.cache.get_role_validation(123456789, "987654321", "test")
        
        self.assertIsNotNone(cached_result)
        self.assertEqual(cached_result.role_id, "987654321")
        self.assertTrue(cached_result.is_valid)
        
        stats = self.cache.get_stats()
        self.assertEqual(stats['role_hits'], 1)
    
    def test_cache_invalidation(self):
        """Test cache invalidation"""
        # Cache a result
        mock_result = RoleValidationResult(role_id="987654321", is_valid=True)
        self.cache.cache_role_validation(123456789, "987654321", mock_result, "test")
        
        # Verify it's cached
        cached_result = self.cache.get_role_validation(123456789, "987654321", "test")
        self.assertIsNotNone(cached_result)
        
        # Invalidate
        self.cache.invalidate_role(123456789, "987654321")
        
        # Verify it's gone
        cached_result = self.cache.get_role_validation(123456789, "987654321", "test")
        self.assertIsNone(cached_result)
    
    def test_cache_expiration(self):
        """Test cache entry expiration"""
        # CacheEntry is imported from main module above
        CacheEntry = spec.CacheEntry
        from datetime import datetime, timedelta
        
        # Create an expired entry
        expired_entry = CacheEntry(
            result="test",
            timestamp=datetime.now() - timedelta(seconds=400),  # 400 seconds ago
            ttl_seconds=300  # 5 minute TTL
        )
        
        self.assertTrue(expired_entry.is_expired())
        
        # Create a fresh entry
        fresh_entry = CacheEntry(
            result="test",
            timestamp=datetime.now(),
            ttl_seconds=300
        )
        
        self.assertFalse(fresh_entry.is_expired())

class TestDatabaseCleanup(unittest.TestCase):
    """Test database cleanup utilities"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 123456789
        self.mock_guild.name = "Test Guild"
        
        self.mock_db = Mock()
    
    def test_scan_team_roles_valid(self):
        """Test scanning team roles with valid data"""
        # Mock database response
        self.mock_db.execute.return_value = [
            ("987654321", "Test Team", "slot_1")
        ]
        
        # Mock guild role lookup
        mock_role = Mock(spec=discord.Role)
        self.mock_guild.get_role.return_value = mock_role
        
        result = DatabaseRoleCleanup.scan_invalid_roles(123456789, self.mock_guild, self.mock_db)
        
        self.assertEqual(result.invalid_roles_found, 0)
        self.assertEqual(len(result.issues), 0)
    
    def test_scan_team_roles_invalid_format(self):
        """Test scanning team roles with invalid format"""
        # Mock database response with invalid role ID
        self.mock_db.execute.return_value = [
            ("invalid_id", "Test Team", "slot_1")
        ]
        
        result = DatabaseRoleCleanup.scan_invalid_roles(123456789, self.mock_guild, self.mock_db)
        
        self.assertGreater(result.invalid_roles_found, 0)
        self.assertGreater(len(result.issues), 0)
        self.assertIn("Invalid role ID format", result.issues[0])
    
    def test_scan_team_roles_not_found(self):
        """Test scanning team roles when role not found in Discord"""
        # Mock database response
        self.mock_db.execute.return_value = [
            ("987654321", "Test Team", "slot_1")
        ]
        
        # Mock guild role lookup - role not found
        self.mock_guild.get_role.return_value = None
        
        result = DatabaseRoleCleanup.scan_invalid_roles(123456789, self.mock_guild, self.mock_db)
        
        self.assertGreater(result.invalid_roles_found, 0)
        self.assertGreater(len(result.issues), 0)
        self.assertIn("Role not found in Discord", result.issues[0])
    
    def test_generate_cleanup_report(self):
        """Test cleanup report generation"""
        # DatabaseCleanupResult is imported from main module above
        DatabaseCleanupResult = spec.DatabaseCleanupResult
        
        result = DatabaseCleanupResult(
            total_roles_checked=5,
            invalid_roles_found=2,
            issues=["Issue 1", "Issue 2"],
            suggestions=["Suggestion 1", "Suggestion 2"]
        )
        
        report = DatabaseRoleCleanup.generate_cleanup_report(self.mock_guild, result)
        
        self.assertIn("Database Role Validation Report", report)
        self.assertIn("Test Guild", report)
        self.assertIn("Roles Checked: 5", report)
        self.assertIn("Invalid Roles Found: 2", report)
        self.assertIn("Issue 1", report)
        self.assertIn("Suggestion 1", report)

class TestIntegration(unittest.TestCase):
    """Integration tests for validation system"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.mock_guild = Mock(spec=discord.Guild)
        self.mock_guild.id = 123456789
        self.mock_guild.name = "Test Guild"
        
        self.mock_role = Mock(spec=discord.Role)
        self.mock_role.id = 987654321
        self.mock_role.name = "Test Role"
        
        self.mock_channel = Mock(spec=discord.TextChannel)
        self.mock_channel.id = 555666777
        self.mock_channel.name = "test-channel"
        
        self.mock_db = Mock()
    
    def test_end_to_end_role_validation(self):
        """Test complete role validation workflow"""
        # Setup mocks
        self.mock_guild.get_role.return_value = self.mock_role
        
        # Test validation
        result = RoleValidator.validate_role_id("987654321", self.mock_guild, "integration test")
        
        # Verify result
        self.assertTrue(result.is_valid)
        self.assertEqual(result.role, self.mock_role)
        self.assertEqual(result.display_name, "Test Role")
        
        # Test error formatting if role was invalid
        if not result.is_valid:
            error_msg = result.error_message
            self.assertIsNotNone(error_msg)
            self.assertIn("How to fix this", error_msg)
    
    def test_end_to_end_channel_validation(self):
        """Test complete channel validation workflow"""
        # Setup mocks
        self.mock_guild.get_channel.return_value = self.mock_channel
        self.mock_db.execute.return_value = [("555666777",)]
        
        # Test validation
        result = ChannelValidator.validate_gametime_channel("slot_1", self.mock_guild, self.mock_db, "Test Slot")
        
        # Verify result
        if result.is_accessible:
            self.assertEqual(result.channel, self.mock_channel)
            self.assertEqual(result.channel_id, "555666777")
        else:
            self.assertIsNotNone(result.error_message)
            self.assertGreater(len(result.setup_guidance), 0)

def run_all_tests():
    """Run all test suites"""
    print("Running comprehensive validation tests...")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestRoleValidation))
    test_suite.addTest(unittest.makeSuite(TestChannelValidation))
    test_suite.addTest(unittest.makeSuite(TestErrorFormatter))
    test_suite.addTest(unittest.makeSuite(TestValidationCache))
    test_suite.addTest(unittest.makeSuite(TestDatabaseCleanup))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'PASS' if success else 'FAIL'}")
    
    return success

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)