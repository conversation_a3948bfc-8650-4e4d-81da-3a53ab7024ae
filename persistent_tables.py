# Functions to ensure persistent tables exist

def ensure_contracts_table_exists():
    """Ensure the contracts table exists - critical for contract persistence on hosting platforms"""
    try:
        # Check if using SQLite and table might not exist
        if hasattr(bot, 'db') and bot.db.db_type == 'sqlite':
            # For SQLite, ensure the table exists
            bot.db.execute('''
            CREATE TABLE IF NOT EXISTS contracts (
                contract_id TEXT PRIMARY KEY,
                slot_id TEXT NOT NULL,
                guild_id TEXT NOT NULL,
                player_id TEXT NOT NULL,
                team_role_id TEXT NOT NULL,
                team_name TEXT NOT NULL,
                contract_amount INTEGER NOT NULL,
                contract_length INTEGER NOT NULL,
                contract_length_unit TEXT DEFAULT 'years',
                time_remaining INTEGER NOT NULL,
                time_remaining_unit TEXT DEFAULT 'years',
                signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_date TIMESTAMP,
                status TEXT DEFAULT 'active',
                notes TEXT
            )''')
            bot.db.commit()
            print("✅ Contracts table verified/created")
        elif hasattr(bot, 'db') and bot.db.db_type == 'supabase':
            # For Supabase, just test that the table exists
            result = bot.db.execute("SELECT COUNT(*) FROM contracts LIMIT 1")
            print("✅ Contracts table exists in Supabase")
    except Exception as e:
        print(f"⚠️ Error ensuring contracts table exists: {e}")
        # If table creation fails, create it manually with simpler approach
        try:
            if hasattr(bot, 'db'):
                bot.db.execute("CREATE TABLE IF NOT EXISTS contracts (contract_id TEXT PRIMARY KEY, slot_id TEXT, guild_id TEXT, player_id TEXT, team_role_id TEXT, team_name TEXT, contract_amount INTEGER, contract_length INTEGER, contract_length_unit TEXT DEFAULT 'years', time_remaining INTEGER, time_remaining_unit TEXT DEFAULT 'years', signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, expires_date TIMESTAMP, status TEXT DEFAULT 'active', notes TEXT)")
                bot.db.commit()
                print("✅ Contracts table created with fallback method")
        except Exception as e2:
            print(f"❌ Critical error: Cannot create contracts table: {e2}")

def ensure_gametimes_table_exists():
    """Ensure the gametimes table exists - critical for game scheduling"""
    try:
        # Check if using SQLite and table might not exist
        if hasattr(bot, 'db') and bot.db.db_type == 'sqlite':
            # For SQLite, ensure the table exists
            bot.db.execute('''
            CREATE TABLE IF NOT EXISTS gametimes (
                game_id TEXT PRIMARY KEY,
                guild_id TEXT NOT NULL,
                slot_id TEXT NOT NULL,
                team1_role_id TEXT NOT NULL,
                team1_name TEXT NOT NULL,
                team1_emoji TEXT,
                team2_role_id TEXT NOT NULL,
                team2_name TEXT NOT NULL,
                team2_emoji TEXT,
                scheduled_time INTEGER NOT NULL,
                timezone TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                channel_id TEXT,
                message_id TEXT,
                created_at INTEGER NOT NULL,
                created_by TEXT NOT NULL,
                referees TEXT DEFAULT '[]',
                streamer TEXT DEFAULT 'null'
            )''')
            bot.db.commit()
            print("✅ Gametimes table verified/created")
        elif hasattr(bot, 'db') and bot.db.db_type == 'supabase':
            # For Supabase, just test that the table exists
            result = bot.db.execute("SELECT COUNT(*) FROM gametimes LIMIT 1")
            print("✅ Gametimes table exists in Supabase")
    except Exception as e:
        print(f"⚠️ Error ensuring gametimes table exists: {e}")
        # If table creation fails, create it manually with simpler approach
        try:
            if hasattr(bot, 'db'):
                bot.db.execute("CREATE TABLE IF NOT EXISTS gametimes (game_id TEXT PRIMARY KEY, guild_id TEXT, slot_id TEXT, team1_role_id TEXT, team1_name TEXT, team1_emoji TEXT, team2_role_id TEXT, team2_name TEXT, team2_emoji TEXT, scheduled_time INTEGER, timezone TEXT, status TEXT DEFAULT 'active', channel_id TEXT, message_id TEXT, created_at INTEGER, created_by TEXT, referees TEXT DEFAULT '[]', streamer TEXT DEFAULT 'null')")
                bot.db.commit()
                print("✅ Gametimes table created with fallback method")
        except Exception as e2:
            print(f"❌ Critical error: Cannot create gametimes table: {e2}")

def initialize_persistent_gametimes():
    """Initialize all persistent tables for the bot"""
    ensure_contracts_table_exists()
    ensure_gametimes_table_exists()
    print("✅ All persistent tables initialized")