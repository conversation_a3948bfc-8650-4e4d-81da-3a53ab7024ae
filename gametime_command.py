import discord
from discord import app_commands
from typing import Optional, Literal
import pytz
from datetime import datetime, timedelta
import random
import time
import json

async def get_user_teams(interaction):
    """Get all teams the user is a member of"""
    teams = []
    
    # Get all teams in the guild
    bot.cursor.execute("SELECT role_id, team_name, emoji, slot_id FROM league_teams WHERE guild_id = ?", (interaction.guild_id,))
    all_teams = bot.cursor.fetchall()
    
    for team_data in all_teams:
        role_id, team_name, emoji, slot_id = team_data
        role = interaction.guild.get_role(int(role_id))
        if role and role in interaction.user.roles:
            teams.append((role, team_name, emoji, slot_id))
    
    return teams

async def select_team_view(interaction, teams):
    """Create a view for selecting a team when user has multiple teams"""
    view = discord.ui.View(timeout=60)
    select = discord.ui.Select(
        placeholder="Select your team",
        options=[discord.SelectOption(
            label=team_name,
            value=str(i),
            emoji=emoji if emoji else "🏆"
        ) for i, (role, team_name, emoji, _) in enumerate(teams)]
    )
    
    selected_team = None
    
    async def select_callback(select_interaction):
        nonlocal selected_team
        index = int(select.values[0])
        selected_team = teams[index]
        view.stop()
        await select_interaction.response.defer()
    
    select.callback = select_callback
    view.add_item(select)
    
    message = await interaction.followup.send("You are on multiple teams. Please select which team you want to schedule a game for:", view=view, ephemeral=True)
    
    await view.wait()
    await message.delete()
    return selected_team

def get_management_permission(interaction):
    """Check if user has management permissions"""
    # Check if user is admin
    if interaction.user.guild_permissions.administrator:
        return True
    
    # Check if user has bot admin role
    guild_settings = bot.db.execute("SELECT admin_role FROM guild_settings WHERE guild_id = ?", (interaction.guild_id,))
    if guild_settings and guild_settings[0] and guild_settings[0][0]:
        admin_role = interaction.guild.get_role(int(guild_settings[0][0]))
        if admin_role and admin_role in interaction.user.roles:
            return True
    
    return False

async def dm_embed_coaches_and_staff(
    guild, user_ids, embed: discord.Embed,
    mention_referees=None, mention_streamers=None
):
    sent = set()
    for user_id in user_ids:
        member = guild.get_member(user_id)
        if member and member.id not in sent:
            extra_mentions = ""
            if mention_referees:
                extra_mentions += f"\nReferees: {mention_referees}"
            if mention_streamers:
                extra_mentions += f"\nStreamers: {mention_streamers}"
            try:
                dm = await member.create_dm()
                await dm.send(content=extra_mentions if extra_mentions else None, embed=embed)
            except Exception:
                pass
            sent.add(member.id)

@bot.tree.command(name="gametime", description="Schedule a game")
@app_commands.describe(
    team1="Select Team 1 (your team, or leave blank to auto-detect)",
    team2="Select Team 2 (opponent team)",
    time="Time of the game (HH:MM)",
    am_pm="AM or PM",
    day="Select the day for the game",
    timezone="Select your timezone"
)
@app_commands.choices(
    day=[
        app_commands.Choice(name="Today", value="today"),
        app_commands.Choice(name="Tomorrow", value="tomorrow"),
        app_commands.Choice(name="In 2 days", value="in_2_days"),
        app_commands.Choice(name="In 3 days", value="in_3_days")
    ]
)
async def gametime(
    interaction: discord.Interaction,
    team1: Optional[discord.Role],
    team2: discord.Role,
    time: str,
    am_pm: Literal["AM", "PM"],
    day: app_commands.Choice[str],
    timezone: Literal["US/Eastern", "US/Central", "US/Mountain", "US/Pacific"]
):
    await interaction.response.defer(ephemeral=True)
    
    try:
        # Permission check
        if team1 is not None and not get_management_permission(interaction) and team1 not in interaction.user.roles:
            await interaction.followup.send("You do not have permission to select Team 1 unless you are on that team, or you are an Admin/Bot Manager.", ephemeral=True)
            return

        # Get team 1 info
        if team1:
            bot.cursor.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE role_id = ?", (str(team1.id),))
            t1_result = bot.cursor.fetchall()
            if not t1_result:
                await interaction.followup.send("Selected Team 1 is not a valid team.", ephemeral=True)
                return
            user_team_role = team1
            user_team_name, user_team_emoji, slot_id_1 = t1_result[0]
            user_team_emoji = user_team_emoji or "🏆"
        else:
            teams = await get_user_teams(interaction)
            if len(teams) == 0:
                await interaction.followup.send("You don't have a team role. Please select Team 1 or join a team.", ephemeral=True)
                return
            elif len(teams) == 1:
                user_team_role, user_team_name, user_team_emoji, slot_id_1 = teams[0]
                user_team_emoji = user_team_emoji or "🏆"
            else:
                selected = await select_team_view(interaction, teams)
                if not selected:
                    await interaction.followup.send("No team selected, cancelling.", ephemeral=True)
                    return
                user_team_role, user_team_name, user_team_emoji, slot_id_1 = selected
                user_team_emoji = user_team_emoji or "🏆"

        # Get team 2 info
        bot.cursor.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE role_id = ?", (str(team2.id),))
        t2_result = bot.cursor.fetchall()
        if not t2_result:
            await interaction.followup.send("Selected Team 2 is not a valid team.", ephemeral=True)
            return
        opponent_role = team2
        opponent_team_name, opponent_team_emoji, slot_id_2 = t2_result[0]
        opponent_team_emoji = opponent_team_emoji or "🏆"

        # Get slot info - check both teams' slots
        slot_ids = []
        if slot_id_1:
            slot_ids.append(slot_id_1)
        if slot_id_2 and slot_id_2 != slot_id_1:
            slot_ids.append(slot_id_2)
        
        # If no slots found, use a default slot if available
        if not slot_ids:
            bot.cursor.execute("SELECT slot_id FROM slot_configs LIMIT 1")
            default_slot = bot.cursor.fetchone()
            if default_slot:
                slot_ids.append(default_slot[0])
        
        # Get gametime channel (with fallback to current channel)
        gametime_channel = None
        
        # Try to find a valid gametime channel from any of the slots
        for slot_id in slot_ids:
            try:
                # Try using cursor first
                bot.cursor.execute("SELECT gametime_channel FROM slot_configs WHERE slot_id = ?", (slot_id,))
                channel_result = bot.cursor.fetchone()
                
                if channel_result and channel_result[0]:
                    try:
                        gametime_channel_id = int(channel_result[0])
                        channel = interaction.guild.get_channel(gametime_channel_id)
                        if channel:
                            gametime_channel = channel
                            print(f"Found gametime channel: {channel.name} ({channel.id})")
                            break
                    except (ValueError, TypeError) as e:
                        print(f"Error converting channel ID: {e}")
                        continue
            except Exception as db_error:
                # Try using db adapter as fallback
                try:
                    print(f"Using db adapter fallback for gametime channel")
                    result = bot.db.execute("SELECT gametime_channel FROM slot_configs WHERE slot_id = ?", (slot_id,))
                    if result and result[0] and result[0][0]:
                        try:
                            gametime_channel_id = int(result[0][0])
                            channel = interaction.guild.get_channel(gametime_channel_id)
                            if channel:
                                gametime_channel = channel
                                print(f"Found gametime channel (fallback): {channel.name} ({channel.id})")
                                break
                        except (ValueError, TypeError) as e:
                            print(f"Error converting channel ID (fallback): {e}")
                            continue
                except Exception as e2:
                    print(f"Error getting gametime channel: {e2}")
        
        # If no valid channel found, use current channel as fallback
        if not gametime_channel:
            gametime_channel = interaction.channel
            print(f"Using current channel as fallback: {gametime_channel.name} ({gametime_channel.id})")

        # Parse time - more robust parsing
        try:
            if ':' not in time:
                await interaction.followup.send("❌ Invalid time format. Please use HH:MM format (e.g., 2:30).", ephemeral=True)
                return
                
            time_parts = time.split(':')
            if len(time_parts) != 2:
                await interaction.followup.send("❌ Invalid time format. Please use HH:MM format (e.g., 2:30).", ephemeral=True)
                return
                
            hour = int(time_parts[0])
            minute = int(time_parts[1])
            
            if hour < 1 or hour > 12:
                await interaction.followup.send("❌ Hour must be between 1 and 12.", ephemeral=True)
                return
            if minute < 0 or minute > 59:
                await interaction.followup.send("❌ Minutes must be between 0 and 59.", ephemeral=True)
                return
            
            # Convert to 24-hour format
            if am_pm == "PM" and hour != 12:
                hour += 12
            elif am_pm == "AM" and hour == 12:
                hour = 0
            
            # Get timezone and current time
            import pytz
            from datetime import datetime, timedelta
            
            days_mapping = {"today": 0, "tomorrow": 1, "in_2_days": 2, "in_3_days": 3}
            user_timezone = pytz.timezone(timezone)
            now = datetime.now(user_timezone)
            target_day = now + timedelta(days=days_mapping[day.value])
            game_datetime = target_day.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # Create Unix timestamp
            unix_timestamp = int(game_datetime.timestamp())
            
            # Format display strings
            time_display = game_datetime.strftime("%I:%M %p").lstrip('0')
            date_display = game_datetime.strftime("%A, %B %d, %Y")
            timezone_short = timezone.split('/')[-1]
            
        except (ValueError, TypeError) as e:
            await interaction.followup.send(f"❌ Error parsing time: {str(e)}. Please use HH:MM format.", ephemeral=True)
            return

        # Generate game ID
        import random
        import time as time_module
        game_id = f"game_{interaction.guild_id}_{int(time_module.time())}_{random.randint(1000, 9999)}"
        
        # Create game view
        class GameView(discord.ui.View):
            def __init__(self, game_id: str):
                super().__init__(timeout=None)
                self.game_id = game_id
                self.referees = []
                self.streamer = None
                self.cancelled = False
            
            @discord.ui.button(label="🏁 Referee", style=discord.ButtonStyle.secondary)
            async def referee_button(self, button_interaction: discord.Interaction, button: discord.ui.Button):
                user_id = button_interaction.user.id
                if user_id in self.referees:
                    self.referees.remove(user_id)
                    await button_interaction.response.send_message("✅ You are no longer refereeing this game.", ephemeral=True)
                else:
                    self.referees.append(user_id)
                    await button_interaction.response.send_message("✅ You are now refereeing this game!", ephemeral=True)
            
            @discord.ui.button(label="📺 Streamer", style=discord.ButtonStyle.secondary)
            async def streamer_button(self, button_interaction: discord.Interaction, button: discord.ui.Button):
                user_id = button_interaction.user.id
                if self.streamer == user_id:
                    self.streamer = None
                    await button_interaction.response.send_message("✅ You are no longer streaming this game.", ephemeral=True)
                else:
                    self.streamer = user_id
                    await button_interaction.response.send_message("✅ You are now streaming this game!", ephemeral=True)
            
            @discord.ui.button(label="❌ Cancel Game", style=discord.ButtonStyle.danger)
            async def cancel_button(self, button_interaction: discord.Interaction, button: discord.ui.Button):
                # Check if user is from either team or has management permissions
                if not (user_team_role in button_interaction.user.roles or 
                        opponent_role in button_interaction.user.roles or 
                        get_management_permission(button_interaction)):
                    await button_interaction.response.send_message("❌ You don't have permission to cancel this game.", ephemeral=True)
                    return
                
                self.cancelled = True
                # Disable all buttons
                for child in self.children:
                    child.disabled = True
                
                # Update the message
                await button_interaction.message.edit(content="⚠️ **GAME CANCELLED** ⚠️", view=self)
                await button_interaction.response.send_message("✅ Game has been cancelled.", ephemeral=True)
        
        # Create the game view instance
        view = GameView(game_id)
        
        # Create the embed
        embed = discord.Embed(
            title=f"Game Scheduled: {user_team_name} vs {opponent_team_name}",
            description=f"<t:{unix_timestamp}:F> (<t:{unix_timestamp}:R>)",
            color=0x3498db
        )
        
        embed.add_field(name="Teams", value=f"{user_team_emoji} {user_team_name} vs {opponent_team_emoji} {opponent_team_name}", inline=False)
        embed.add_field(name="Time", value=f"{time_display} {timezone_short}", inline=True)
        embed.add_field(name="Date", value=f"{date_display}", inline=True)
        embed.set_footer(text=f"Game ID: {game_id} | Scheduled by {interaction.user.display_name}")
        
        # Send the game announcement to the gametime channel
        try:
            game_message = await gametime_channel.send(embed=embed, view=view)
            
            # Import the initialize_persistent_gametimes function
            try:
                from persistent_tables import initialize_persistent_gametimes
                initialize_persistent_gametimes()
            except ImportError:
                print("Could not import persistent_tables module, using fallback method")
                try:
                    # Create gametimes table if it doesn't exist
                    bot.db.execute('''
                    CREATE TABLE IF NOT EXISTS gametimes (
                        game_id TEXT PRIMARY KEY,
                        guild_id TEXT NOT NULL,
                        slot_id TEXT NOT NULL,
                        team1_role_id TEXT NOT NULL,
                        team1_name TEXT NOT NULL,
                        team1_emoji TEXT,
                        team2_role_id TEXT NOT NULL,
                        team2_name TEXT NOT NULL,
                        team2_emoji TEXT,
                        scheduled_time INTEGER NOT NULL,
                        timezone TEXT NOT NULL,
                        status TEXT DEFAULT 'active',
                        channel_id TEXT,
                        message_id TEXT,
                        created_at INTEGER NOT NULL,
                        created_by TEXT NOT NULL,
                        referees TEXT DEFAULT '[]',
                        streamer TEXT DEFAULT 'null'
                    )''')
                    bot.db.commit()
                except Exception as e:
                    print(f"Error creating gametimes table: {e}")
            
            # Store game in database with proper data types
            try:
                print("Attempting to store game in database...")
                bot.cursor.execute(
                    "INSERT INTO gametimes (game_id, guild_id, slot_id, team1_role_id, team1_name, team1_emoji, team2_role_id, team2_name, team2_emoji, scheduled_time, timezone, message_id, channel_id, created_at, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (game_id, str(interaction.guild_id), slot_ids[0] if slot_ids else "default", str(user_team_role.id), user_team_name, user_team_emoji, str(opponent_role.id), opponent_team_name, opponent_team_emoji, unix_timestamp, timezone, str(game_message.id), str(gametime_channel.id), int(time_module.time()), str(interaction.user.id))
                )
                bot.conn.commit()
                print("Game stored successfully using cursor")
            except Exception as db_error:
                print(f"Database error when storing game: {db_error}")
                # Try with a more compatible approach
                try:
                    print("Trying alternative method to store game...")
                    bot.db.execute(
                        "INSERT INTO gametimes (game_id, guild_id, slot_id, team1_role_id, team1_name, team1_emoji, team2_role_id, team2_name, team2_emoji, scheduled_time, timezone, message_id, channel_id, created_at, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        (game_id, str(interaction.guild_id), slot_ids[0] if slot_ids else "default", str(user_team_role.id), user_team_name, user_team_emoji, str(opponent_role.id), opponent_team_name, opponent_team_emoji, unix_timestamp, timezone, str(game_message.id), str(gametime_channel.id), int(time_module.time()), str(interaction.user.id))
                    )
                    bot.db.commit()
                    print("Game stored successfully using db adapter")
                except Exception as e2:
                    print(f"Failed to store game using alternative method: {e2}")
                    await interaction.followup.send(f"❌ Error storing game in database: {str(e2)}", ephemeral=True)
                    return
            
            # Notify the user
            await interaction.followup.send(f"✅ Game scheduled successfully in {gametime_channel.mention}!", ephemeral=True)
            
            # Ping the teams
            await gametime_channel.send(f"{user_team_role.mention} {opponent_role.mention} Game scheduled!")
            
        except Exception as e:
            await interaction.followup.send(f"❌ Error scheduling game: {str(e)}", ephemeral=True)
            
    except Exception as e:
        await interaction.followup.send(f"❌ An error occurred: {str(e)}", ephemeral=True)