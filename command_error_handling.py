# =========================
# Comprehensive Error Handling for Role-Dependent Commands
# =========================
"""
This module provides comprehensive error handling wrappers and utilities for all
role-dependent commands to ensure consistent validation and error messaging.

Requirements addressed:
- 1.1: <PERSON>le invalid role IDs gracefully in all commands
- 1.4: Provide actionable guidance when role validation fails
- 4.4: Ensure consistent error messaging across all affected commands
- 4.5: Replace direct role access patterns with safe validation
"""

from typing import List, Dict, Any, Optional, Callable, Union
from functools import wraps
import discord
import traceback
import logging
# Import from the main bot file instead of separate modules
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the integrated classes from the main bot file
from importlib import import_module
spec = import_module('import sqlite3')
RoleValidator = spec.RoleValidator
RoleValidationResult = spec.RoleValidationResult
ChannelValidator = spec.ChannelValidator
ChannelValidationResult = spec.ChannelValidationResult
ErrorFormatter = spec.ErrorFormatter
ValidationError = spec.ValidationError
DatabaseRoleCleanup = spec.DatabaseRoleCleanup

class CommandErrorHandler:
    """Centralized error handling for role-dependent commands"""
    
    @staticmethod
    def safe_role_command(command_name: str, require_roles: List[str] = None):
        """
        Decorator to add comprehensive role validation to commands
        
        Args:
            command_name: Name of the command for error messages
            require_roles: List of role types that must be valid for command to proceed
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Extract interaction from args (should be first argument after self)
                interaction = None
                for arg in args:
                    if isinstance(arg, discord.Interaction):
                        interaction = arg
                        break
                
                if not interaction:
                    logging.error(f"Could not find interaction in {command_name} command arguments")
                    return await func(*args, **kwargs)
                
                try:
                    # Pre-validate any role parameters
                    validation_errors = []
                    
                    # Check for role parameters in kwargs
                    for param_name, param_value in kwargs.items():
                        if isinstance(param_value, discord.Role):
                            role_validation = RoleValidator.validate_role_id(
                                str(param_value.id), 
                                interaction.guild, 
                                f"{command_name} command parameter '{param_name}'"
                            )
                            
                            if not role_validation.is_valid:
                                validation_errors.append(ValidationError(
                                    error_type=role_validation.error_type,
                                    item_type="role",
                                    item_id=str(param_value.id),
                                    item_name=param_value.name,
                                    context=f"{command_name} command",
                                    suggestions=role_validation.suggestions
                                ))
                    
                    # If there are validation errors, send error message and return
                    if validation_errors:
                        error_message = ErrorFormatter.multiple_validation_errors(
                            validation_errors, 
                            f"{command_name} command execution"
                        )
                        
                        if interaction.response.is_done():
                            await interaction.followup.send(error_message, ephemeral=True)
                        else:
                            await interaction.response.send_message(error_message, ephemeral=True)
                        return
                    
                    # Execute the original command
                    return await func(*args, **kwargs)
                    
                except Exception as e:
                    # Catch any unhandled errors and provide user-friendly message
                    logging.error(f"Error in {command_name} command: {e}")
                    traceback.print_exc()
                    
                    error_message = ErrorFormatter.command_failure_error(
                        command_name,
                        f"An unexpected error occurred: {str(e)}",
                        [
                            "Try the command again in a few moments",
                            "If the issue persists, contact an administrator",
                            "Check that all roles and channels are properly configured"
                        ]
                    )
                    
                    try:
                        if interaction.response.is_done():
                            await interaction.followup.send(error_message, ephemeral=True)
                        else:
                            await interaction.response.send_message(error_message, ephemeral=True)
                    except:
                        # If we can't send the error message, at least log it
                        logging.error(f"Failed to send error message for {command_name} command")
                    
                    return
            
            return wrapper
        return decorator
    
    @staticmethod
    async def validate_team_roles(interaction: discord.Interaction, team_roles: List[discord.Role], command_name: str) -> Optional[List[ValidationError]]:
        """
        Validate multiple team roles for a command
        
        Args:
            interaction: Discord interaction object
            team_roles: List of team role objects to validate
            command_name: Name of the command for error context
            
        Returns:
            List of ValidationError objects if validation fails, None if all valid
        """
        validation_errors = []
        
        for i, team_role in enumerate(team_roles, 1):
            if not team_role:
                continue
                
            # Check if team role is registered in database
            try:
                team_check = interaction.client.db.execute(
                    "SELECT team_name, slot_id FROM league_teams WHERE guild_id = ? AND role_id = ?",
                    (interaction.guild_id, str(team_role.id))
                )
                
                if not team_check:
                    validation_errors.append(ValidationError(
                        error_type="not_registered",
                        item_type="role",
                        item_id=str(team_role.id),
                        item_name=team_role.name,
                        context=f"Team {i} in {command_name}",
                        suggestions=[
                            f"Use /add_team to register {team_role.name} as a team",
                            "Ensure the role is added to a slot configuration",
                            "Use /list_teams to see all registered teams"
                        ]
                    ))
                    continue
                
                # Validate the role itself
                role_validation = RoleValidator.validate_team_role(
                    str(team_role.id), 
                    interaction.guild, 
                    team_role.name
                )
                
                if not role_validation.is_valid:
                    validation_errors.append(ValidationError(
                        error_type=role_validation.error_type,
                        item_type="role",
                        item_id=str(team_role.id),
                        item_name=team_role.name,
                        context=f"Team {i} in {command_name}",
                        suggestions=role_validation.suggestions
                    ))
                    
            except Exception as e:
                validation_errors.append(ValidationError(
                    error_type="database_error",
                    item_type="role",
                    item_id=str(team_role.id),
                    item_name=team_role.name,
                    context=f"Team {i} validation in {command_name}",
                    suggestions=[
                        "Try the command again in a few moments",
                        "Contact an administrator if the issue persists"
                    ]
                ))
        
        return validation_errors if validation_errors else None
    
    @staticmethod
    async def validate_management_permissions(interaction: discord.Interaction, team_role: discord.Role, command_name: str) -> Optional[str]:
        """
        Validate that user has management permissions for a team
        
        Args:
            interaction: Discord interaction object
            team_role: Team role to check permissions for
            command_name: Name of the command for error context
            
        Returns:
            Error message if validation fails, None if valid
        """
        try:
            # Get team information
            team_check = interaction.client.db.execute(
                "SELECT team_name, slot_id FROM league_teams WHERE guild_id = ? AND role_id = ?",
                (interaction.guild_id, str(team_role.id))
            )
            
            if not team_check:
                return f"❌ **Team Not Found**: {team_role.mention} is not registered as a team. Use `/add_team` to register it first."
            
            team_name, slot_id = team_check[0]
            
            # Get management roles for the slot
            mgmt_check = interaction.client.db.execute(
                "SELECT franchise_owner_role, general_manager_role FROM slot_configs WHERE slot_id = ?",
                (slot_id,)
            )
            
            if not mgmt_check:
                return f"❌ **Configuration Error**: No management roles configured for this team's slot. Use `/setup` to configure management roles."
            
            fo_role_id, gm_role_id = mgmt_check[0]
            
            # Check if user has management permissions
            user_role_ids = [str(role.id) for role in interaction.user.roles]
            
            has_management = False
            if fo_role_id and fo_role_id in user_role_ids:
                has_management = True
            if gm_role_id and gm_role_id in user_role_ids:
                has_management = True
            
            # Check if user is server admin
            if interaction.user.guild_permissions.administrator:
                has_management = True
            
            if not has_management:
                error_msg = f"❌ **Permission Denied**: You don't have management permissions for {team_role.mention}.\n\n"
                error_msg += f"💡 **Required Permissions**:\n"
                
                if fo_role_id:
                    fo_role = interaction.guild.get_role(int(fo_role_id))
                    if fo_role:
                        error_msg += f"• {fo_role.mention} (Franchise Owner)\n"
                
                if gm_role_id:
                    gm_role = interaction.guild.get_role(int(gm_role_id))
                    if gm_role:
                        error_msg += f"• {gm_role.mention} (General Manager)\n"
                
                error_msg += f"• Server Administrator permissions"
                
                return error_msg
            
            return None
            
        except Exception as e:
            logging.error(f"Error validating management permissions in {command_name}: {e}")
            return f"❌ **Permission Check Failed**: Could not verify management permissions. Please try again or contact an administrator."
    
    @staticmethod
    async def handle_database_error(interaction: discord.Interaction, operation: str, error: Exception, command_name: str):
        """
        Handle database errors consistently across commands
        
        Args:
            interaction: Discord interaction object
            operation: Description of the database operation that failed
            error: The exception that occurred
            command_name: Name of the command for error context
        """
        logging.error(f"Database error in {command_name} command during {operation}: {error}")
        traceback.print_exc()
        
        error_message = ErrorFormatter.database_error(operation, str(error))
        
        try:
            if interaction.response.is_done():
                await interaction.followup.send(error_message, ephemeral=True)
            else:
                await interaction.response.send_message(error_message, ephemeral=True)
        except:
            logging.error(f"Failed to send database error message for {command_name} command")
    
    @staticmethod
    async def validate_slot_configuration(interaction: discord.Interaction, slot_id: str, command_name: str) -> Optional[List[ValidationError]]:
        """
        Validate slot configuration for commands that depend on slot settings
        
        Args:
            interaction: Discord interaction object
            slot_id: Slot ID to validate
            command_name: Name of the command for error context
            
        Returns:
            List of ValidationError objects if validation fails, None if valid
        """
        validation_errors = []
        
        try:
            # Get slot configuration
            config_result = interaction.client.db.execute(
                "SELECT franchise_owner_role, general_manager_role, head_coach_role, assistant_coach_role, gametime_channel FROM slot_configs WHERE slot_id = ?",
                (slot_id,)
            )
            
            if not config_result:
                validation_errors.append(ValidationError(
                    error_type="not_configured",
                    item_type="slot",
                    item_id=slot_id,
                    context=f"{command_name} command",
                    suggestions=[
                        f"Use /setup to configure slot settings",
                        f"Ensure the slot exists and is properly set up"
                    ]
                ))
                return validation_errors
            
            config = config_result[0]
            fo_role, gm_role, hc_role, ac_role, gametime_channel = config
            
            # Validate management roles
            role_configs = {
                'franchise_owner_role': fo_role,
                'general_manager_role': gm_role,
                'head_coach_role': hc_role,
                'assistant_coach_role': ac_role
            }
            
            role_validation_results = RoleValidator.validate_management_roles(slot_id, interaction.guild, role_configs)
            role_errors = RoleValidator.get_validation_errors(role_validation_results)
            validation_errors.extend(role_errors)
            
            # Validate gametime channel if needed for this command
            if gametime_channel and command_name in ['gametime', 'schedule']:
                channel_validation = ChannelValidator.validate_gametime_channel(slot_id, interaction.guild, interaction.client.db)
                if not channel_validation.is_accessible:
                    validation_errors.append(ValidationError(
                        error_type=channel_validation.error_type,
                        item_type="channel",
                        item_id=channel_validation.channel_id or "",
                        context=f"gametime channel for {command_name}",
                        suggestions=channel_validation.setup_guidance
                    ))
            
        except Exception as e:
            validation_errors.append(ValidationError(
                error_type="database_error",
                item_type="slot",
                item_id=slot_id,
                context=f"slot validation for {command_name}",
                suggestions=[
                    "Try the command again in a few moments",
                    "Contact an administrator if the issue persists"
                ]
            ))
        
        return validation_errors if validation_errors else None

# Utility functions for easy integration
async def safe_get_team_role(interaction: discord.Interaction, role_id: str, command_name: str) -> Optional[discord.Role]:
    """Safely get a team role with validation"""
    role_validation = RoleValidator.validate_role_id(role_id, interaction.guild, f"{command_name} command")
    
    if not role_validation.is_valid:
        error_message = role_validation.error_message
        try:
            if interaction.response.is_done():
                await interaction.followup.send(error_message, ephemeral=True)
            else:
                await interaction.response.send_message(error_message, ephemeral=True)
        except:
            logging.error(f"Failed to send role validation error in {command_name}")
        return None
    
    return role_validation.role

async def validate_command_roles(interaction: discord.Interaction, roles: List[discord.Role], command_name: str) -> bool:
    """Validate multiple roles for a command, return True if all valid"""
    validation_errors = await CommandErrorHandler.validate_team_roles(interaction, roles, command_name)
    
    if validation_errors:
        error_message = ErrorFormatter.multiple_validation_errors(validation_errors, f"{command_name} command")
        try:
            if interaction.response.is_done():
                await interaction.followup.send(error_message, ephemeral=True)
            else:
                await interaction.response.send_message(error_message, ephemeral=True)
        except:
            logging.error(f"Failed to send validation error in {command_name}")
        return False
    
    return True

def log_command_error(command_name: str, error: Exception, interaction: discord.Interaction = None):
    """Log command errors consistently"""
    guild_info = f" (Guild: {interaction.guild_id})" if interaction else ""
    user_info = f" (User: {interaction.user.id})" if interaction else ""
    
    logging.error(f"Error in {command_name} command{guild_info}{user_info}: {error}")
    traceback.print_exc()