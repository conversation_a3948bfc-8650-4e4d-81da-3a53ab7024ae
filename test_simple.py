#!/usr/bin/env python3
"""
Simple test to verify the integrated validation system works
"""

# Test basic import
try:
    import importlib.util
    import sys
    
    # Load the module directly
    spec = importlib.util.spec_from_file_location("bot_module", "import sqlite3.py")
    bot_module = importlib.util.module_from_spec(spec)
    
    print("Loading module...")
    spec.loader.exec_module(bot_module)
    
    print("✅ Module loaded successfully!")
    
    # Test that our classes exist
    print("Testing class availability:")
    print(f"  RoleValidator: {hasattr(bot_module, 'RoleValidator')}")
    print(f"  ErrorFormatter: {hasattr(bot_module, 'ErrorFormatter')}")
    print(f"  ChannelValidator: {hasattr(bot_module, 'ChannelValidator')}")
    print(f"  ValidationCache: {hasattr(bot_module, 'ValidationCache')}")
    print(f"  DatabaseRoleCleanup: {hasattr(bot_module, 'DatabaseRoleCleanup')}")
    
    # Test creating instances (should not raise errors)
    try:
        error_formatter = bot_module.ErrorFormatter()
        print("✅ ErrorFormatter instance created")
    except Exception as e:
        print(f"❌ Error creating ErrorFormatter: {e}")
        
    try:
        validation_cache = bot_module.ValidationCache()
        print("✅ ValidationCache instance created")
    except Exception as e:
        print(f"❌ Error creating ValidationCache: {e}")
    
    print("\n✅ All tests passed! The integration was successful.")
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
