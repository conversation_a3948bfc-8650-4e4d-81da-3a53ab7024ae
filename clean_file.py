#!/usr/bin/env python3
"""
Clean up the bloated import sqlite3.py file by removing excessive whitespace and empty functions
"""

def clean_file():
    # Read the original file
    with open('import sqlite3.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    cleaned_lines = []
    empty_line_count = 0
    
    print(f"Original file has {len(lines)} lines")
    
    for i, line in enumerate(lines):
        stripped = line.strip()
        
        # Handle empty lines - limit to max 1 consecutive empty line
        if not stripped:
            empty_line_count += 1
            if empty_line_count <= 1:
                cleaned_lines.append(line)
            continue
        else:
            empty_line_count = 0
        
        # Skip lines that are just whitespace variations
        if stripped in ['', '    ', '        ', '            ', 'pass']:
            continue
            
        # Keep all other lines
        cleaned_lines.append(line)
    
    # Join the cleaned lines
    cleaned_content = '\n'.join(cleaned_lines)
    
    # Write the cleaned file
    with open('import sqlite3_cleaned.py', 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print(f'File cleaned and saved as import sqlite3_cleaned.py')
    print(f'Original lines: {len(lines)}')
    print(f'Cleaned lines: {len(cleaned_lines)}')
    print(f'Removed lines: {len(lines) - len(cleaned_lines)}')
    
    # Test syntax
    try:
        with open('import sqlite3_cleaned.py', 'r', encoding='utf-8') as f:
            import ast
            ast.parse(f.read())
        print("✅ Cleaned file syntax is valid!")
    except SyntaxError as e:
        print(f"❌ Syntax error in cleaned file: {e}")

if __name__ == "__main__":
    clean_file()
