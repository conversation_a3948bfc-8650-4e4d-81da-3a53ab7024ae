#!/usr/bin/env python3
"""
Test script for whitelist/blacklist functionality consistency
"""

import sqlite3
import uuid
import sys
import os

def test_whitelist_blacklist_consistency():
    """
    Test the whitelist/blacklist functionality for database consistency
    """
    # Create a test database
    test_db_path = "test_whitelist_blacklist.db"
    
    # Remove existing test database
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    conn = sqlite3.connect(test_db_path)
    cursor = conn.cursor()
    
    try:
        print("🧪 Testing whitelist/blacklist database consistency...")
        
        # Create the tables as they would be created in the bot
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS slot_team_whitelist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                slot_id TEXT NOT NULL,
                team_role_id TEXT NOT NULL,
                UNIQUE(slot_id, team_role_id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS slot_team_blacklist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                slot_id TEXT NOT NULL,
                team_role_id TEXT NOT NULL,
                UNIQUE(slot_id, team_role_id)
            )
        """)
        
        conn.commit()
        print("✅ Tables created successfully")
        
        # Test data
        test_slot_id = uuid.uuid4().hex
        test_team_role_id = "123456789"
        
        # Test whitelist operations
        print("\n🧪 Testing whitelist operations...")
        
        # Add to whitelist
        cursor.execute(
            "INSERT INTO slot_team_whitelist (slot_id, team_role_id) VALUES (?, ?)",
            (test_slot_id, test_team_role_id)
        )
        conn.commit()
        
        # Check if exists
        cursor.execute(
            "SELECT COUNT(*) FROM slot_team_whitelist WHERE slot_id = ? AND team_role_id = ?",
            (test_slot_id, test_team_role_id)
        )
        count = cursor.fetchone()[0]
        assert count == 1, f"Expected 1 whitelist entry, got {count}"
        print("✅ Whitelist add operation successful")
        
        # Test duplicate prevention
        try:
            cursor.execute(
                "INSERT INTO slot_team_whitelist (slot_id, team_role_id) VALUES (?, ?)",
                (test_slot_id, test_team_role_id)
            )
            conn.commit()
            print("❌ Duplicate whitelist entry should have been prevented")
            return False
        except sqlite3.IntegrityError:
            print("✅ Duplicate whitelist prevention working")
        
        # Test blacklist operations
        print("\n🧪 Testing blacklist operations...")
        
        # Add to blacklist
        cursor.execute(
            "INSERT INTO slot_team_blacklist (slot_id, team_role_id) VALUES (?, ?)",
            (test_slot_id, test_team_role_id)
        )
        conn.commit()
        
        # Check if exists
        cursor.execute(
            "SELECT COUNT(*) FROM slot_team_blacklist WHERE slot_id = ? AND team_role_id = ?",
            (test_slot_id, test_team_role_id)
        )
        count = cursor.fetchone()[0]
        assert count == 1, f"Expected 1 blacklist entry, got {count}"
        print("✅ Blacklist add operation successful")
        
        # Test removal operations
        print("\n🧪 Testing removal operations...")
        
        # Remove from whitelist
        cursor.execute(
            "DELETE FROM slot_team_whitelist WHERE slot_id = ? AND team_role_id = ?",
            (test_slot_id, test_team_role_id)
        )
        conn.commit()
        
        cursor.execute(
            "SELECT COUNT(*) FROM slot_team_whitelist WHERE slot_id = ? AND team_role_id = ?",
            (test_slot_id, test_team_role_id)
        )
        count = cursor.fetchone()[0]
        assert count == 0, f"Expected 0 whitelist entries after removal, got {count}"
        print("✅ Whitelist removal operation successful")
        
        # Remove from blacklist
        cursor.execute(
            "DELETE FROM slot_team_blacklist WHERE slot_id = ? AND team_role_id = ?",
            (test_slot_id, test_team_role_id)
        )
        conn.commit()
        
        cursor.execute(
            "SELECT COUNT(*) FROM slot_team_blacklist WHERE slot_id = ? AND team_role_id = ?",
            (test_slot_id, test_team_role_id)
        )
        count = cursor.fetchone()[0]
        assert count == 0, f"Expected 0 blacklist entries after removal, got {count}"
        print("✅ Blacklist removal operation successful")
        
        # Test bulk operations
        print("\n🧪 Testing bulk operations...")
        
        # Add multiple teams to whitelist
        test_teams = ["111111111", "222222222", "333333333"]
        for team_id in test_teams:
            cursor.execute(
                "INSERT INTO slot_team_whitelist (slot_id, team_role_id) VALUES (?, ?)",
                (test_slot_id, team_id)
            )
        conn.commit()
        
        # Verify count
        cursor.execute(
            "SELECT COUNT(*) FROM slot_team_whitelist WHERE slot_id = ?",
            (test_slot_id,)
        )
        count = cursor.fetchone()[0]
        assert count == len(test_teams), f"Expected {len(test_teams)} whitelist entries, got {count}"
        print("✅ Bulk whitelist add operation successful")
        
        # Clear all for the slot
        cursor.execute("DELETE FROM slot_team_whitelist WHERE slot_id = ?", (test_slot_id,))
        conn.commit()
        
        cursor.execute(
            "SELECT COUNT(*) FROM slot_team_whitelist WHERE slot_id = ?",
            (test_slot_id,)
        )
        count = cursor.fetchone()[0]
        assert count == 0, f"Expected 0 whitelist entries after clear, got {count}"
        print("✅ Bulk whitelist clear operation successful")
        
        print("\n🎉 All whitelist/blacklist database tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()
        # Clean up test database
        if os.path.exists(test_db_path):
            os.remove(test_db_path)

if __name__ == "__main__":
    success = test_whitelist_blacklist_consistency()
    sys.exit(0 if success else 1)
