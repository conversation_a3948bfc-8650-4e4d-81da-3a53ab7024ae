# Removed commands - saved for future reference
# These commands were removed as their functionality was integrated into wizards or deemed unnecessary

# Live Management Commands (now in setup wizard)
@bot.tree.command(name="live_management_setup", description="Create/set up a live management list in a channel.")
@app_commands.describe(
    channel="Channel to post the list in",
    display_type="Which management roles to display (owners/gms/both)",
    auto_update="Auto-update every hour?",
    include_empty="Show teams with no management?",
    league_style="NFL or College style naming",
    secondary_label="(College only) Use AD or Head Coach label?",
    slot="Slot ID or name (leave blank for all/default)"
)
@app_commands.choices(display_type=[
    app_commands.Choice(name="Owners Only", value="owners"),
    app_commands.Choice(name="GMs Only", value="gms"),
    app_commands.Choice(name="Both Owners & GMs", value="both")
])
@app_commands.choices(auto_update=[
    app_commands.Choice(name="Yes", value="yes"),
    app_commands.Choice(name="No", value="no")
])
@app_commands.choices(include_empty=[
    app_commands.Choice(name="Yes", value="yes"),
    app_commands.Choice(name="No", value="no")
])
@app_commands.choices(league_style=[
    app_commands.Choice(name="NFL", value="nfl"),
    app_commands.Choice(name="College", value="college")
])
@app_commands.choices(secondary_label=[
    app_commands.Choice(name="Athletic Director (AD)", value="AD"),
    app_commands.Choice(name="Head Coach (HC)", value="HC")
])
async def live_management_setup(
    interaction: discord.Interaction, 
    channel: discord.TextChannel,
    display_type: app_commands.Choice[str],
    auto_update: app_commands.Choice[str] = "yes",
    include_empty: app_commands.Choice[str] = "yes",
    league_style: app_commands.Choice[str] = "nfl",
    secondary_label: app_commands.Choice[str] = "AD",
    slot: str = None
):
    await interaction.response.defer(ephemeral=True)
    try:
        ensure_management_table()
        guild_id = str(interaction.guild.id)
        channel_id = str(channel.id)
        
        # Get slot ID from input (or default)
        slot_id = get_slot_id_from_input(guild_id, slot) if slot else None
        if not slot_id:
            # Get default slot
            result = bot.db.execute("SELECT slot_id FROM slots WHERE guild_id = ? AND is_default = 1", (guild_id,))
            if result and result[0]:
                slot_id = result[0][0]
            else:
                # Create a default slot if none exists
                slot_id = uuid.uuid4().hex
                bot.db.execute("INSERT INTO slots (slot_id, guild_id, slot_name, is_default) VALUES (?, ?, ?, 1)", 
                              (slot_id, guild_id, "DEFAULT", ))
        
        # Check if a management list already exists for this channel/slot
        result = bot.db.execute("""
        SELECT * FROM live_management_lists 
        WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
        """, (guild_id, channel_id, slot_id))
        
        if result:
            # Update existing
            bot.db.execute("""
            UPDATE live_management_lists 
            SET display_type = ?, auto_update = ?, include_empty = ?, league_style = ?, secondary_label = ?, active = 'yes'
            WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
            """, (display_type.value, auto_update.value, include_empty.value, league_style.value, secondary_label.value, 
                 guild_id, channel_id, slot_id))
            action = "updated"
        else:
            # Create new
            bot.db.execute("""
            INSERT INTO live_management_lists 
            (guild_id, channel_id, slot_id, display_type, auto_update, include_empty, league_style, secondary_label, active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'yes')
            """, (guild_id, channel_id, slot_id, display_type.value, auto_update.value, include_empty.value, 
                 league_style.value, secondary_label.value))
            action = "created"
        
        # Create and send the management embed
        await update_management_embed(interaction.guild, channel, slot_id, display_type.value, 
                                     include_empty.value, league_style.value, secondary_label.value)
        
        await interaction.followup.send(f"✅ Successfully {action} management list in {channel.mention}!", ephemeral=True)
    except Exception as e:
        print(f"Error in live_management_setup: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error setting up live management list: {e}", ephemeral=True)

@bot.tree.command(name="live_management_disable", description="Remove/disable a live management list in a channel/slot.")
@app_commands.describe(
    channel="Channel to disable list in",
    slot="Slot ID or name (optional, disables all/default if not supplied)"
)
async def live_management_disable(interaction: discord.Interaction, channel: discord.TextChannel, slot: str = None):
    await interaction.response.defer(ephemeral=True)
    try:
        ensure_management_table()
        guild_id = str(interaction.guild.id)
        channel_id = str(channel.id)
        
        if slot:
            # Disable specific slot
            slot_id = get_slot_id_from_input(guild_id, slot)
            if not slot_id:
                await interaction.followup.send(f"❌ Slot '{slot}' not found.", ephemeral=True)
                return
            
            bot.db.execute("""
            UPDATE live_management_lists 
            SET active = 'no' 
            WHERE guild_id = ? AND channel_id = ? AND slot_id = ?
            """, (guild_id, channel_id, slot_id))
            
            await interaction.followup.send(f"✅ Disabled management list for slot '{slot}' in {channel.mention}.", ephemeral=True)
        else:
            # Disable all slots in this channel
            bot.db.execute("""
            UPDATE live_management_lists 
            SET active = 'no' 
            WHERE guild_id = ? AND channel_id = ?
            """, (guild_id, channel_id))
            
            await interaction.followup.send(f"✅ Disabled all management lists in {channel.mention}.", ephemeral=True)
    except Exception as e:
        print(f"Error in live_management_disable: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error disabling management list: {e}", ephemeral=True)

@bot.tree.command(name="live_management_update", description="Manually refresh a management list in a channel/slot.")
@app_commands.describe(
    channel="Channel to update",
    slot="Slot ID or name (optional)"
)
async def live_management_update(interaction: discord.Interaction, channel: discord.TextChannel, slot: str = None):
    await interaction.response.defer(ephemeral=True)
    try:
        guild_id = str(interaction.guild.id)
        slot_id = get_slot_id_from_input(guild_id, slot) if slot else None
        if not slot_id:
            # Get all active management lists for this channel
            result = bot.db.execute("""
            SELECT slot_id, display_type, include_empty, league_style, secondary_label
            FROM live_management_lists
            WHERE guild_id = ? AND channel_id = ? AND active = 'yes'
            """, (guild_id, str(channel.id)))
            
            if not result:
                await interaction.followup.send(f"❌ No active management lists found in {channel.mention}.", ephemeral=True)
                return
            
            for row in result:
                slot_id, display_type, include_empty, league_style, secondary_label = row
                await update_management_embed(interaction.guild, channel, slot_id, display_type, include_empty, league_style, secondary_label)
            
            await interaction.followup.send(f"✅ Updated all management lists in {channel.mention}.", ephemeral=True)
        else:
            # Update specific slot
            result = bot.db.execute("""
            SELECT display_type, include_empty, league_style, secondary_label
            FROM live_management_lists
            WHERE guild_id = ? AND channel_id = ? AND slot_id = ? AND active = 'yes'
            """, (guild_id, str(channel.id), slot_id))
            
            if not result:
                await interaction.followup.send(f"❌ No active management list found for slot '{slot}' in {channel.mention}.", ephemeral=True)
                return
            
            display_type, include_empty, league_style, secondary_label = result[0]
            await update_management_embed(interaction.guild, channel, slot_id, display_type, include_empty, league_style, secondary_label)
            
            await interaction.followup.send(f"✅ Updated management list for slot '{slot}' in {channel.mention}.", ephemeral=True)
    except Exception as e:
        print(f"Error in live_management_update: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error updating management list: {e}", ephemeral=True)

@bot.tree.command(name="live_management_list", description="Show where all live management lists are configured.")
async def live_management_list(interaction: discord.Interaction):
    await interaction.response.defer(ephemeral=True)
    try:
        ensure_management_table()
        guild_id = str(interaction.guild.id)
        
        result = bot.db.execute("""
        SELECT channel_id, slot_id, display_type, league_style, secondary_label, auto_update, include_empty, active
        FROM live_management_lists
        WHERE guild_id = ?
        """, (guild_id,))
        rows = result if result else []
        if not rows:
            await interaction.followup.send("No live management lists are currently configured.", ephemeral=True)
            return
        embed = discord.Embed(
            title=f"Live Management Lists for {interaction.guild.name}",
            color=discord.Color.blue()
        )
        for r in rows:
            channel_id, slot_id, display_type, league_style, secondary_label, auto_update, include_empty, active = safe_extract_values(r, [0, 1, 2, 3, 4, 5, 6, 7])
            
            # Get channel and slot names
            try:
                channel = interaction.guild.get_channel(int(channel_id))
                channel_name = channel.mention if channel else f"<#{channel_id}> (deleted)"
            except:
                channel_name = f"<#{channel_id}> (error)"
            
            slot_result = bot.db.execute("SELECT slot_name FROM slots WHERE slot_id = ?", (slot_id,))
            slot_name = slot_result[0][0] if slot_result and slot_result[0] else f"Unknown ({slot_id[:8]}...)"
            
            status = "✅ Active" if active == "yes" else "❌ Disabled"
            auto = "✅ Yes" if auto_update == "yes" else "❌ No"
            empty = "✅ Yes" if include_empty == "yes" else "❌ No"
            
            embed.add_field(
                name=f"{channel_name} - {slot_name}",
                value=(
                    f"**Status:** {status}\n"
                    f"**Display:** {display_type.title()}\n"
                    f"**Auto-Update:** {auto}\n"
                    f"**Show Empty:** {empty}\n"
                    f"**Style:** {league_style.title()}"
                ),
                inline=True
            )
        
        await interaction.followup.send(embed=embed, ephemeral=True)
    except Exception as e:
        print(f"Error in live_management_list: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error listing management lists: {e}", ephemeral=True)

# Developer Commands (now consolidated in developer panel)
@bot.tree.command(name="developer_reset", description="[DEPRECATED] Use /developer panel instead")
async def reset_all_guilds(interaction: discord.Interaction):
    # Restrict to bot owner only
    if interaction.user.id != 782671382685024279:
        await interaction.response.send_message("❌ This command is only available to the bot developer.", ephemeral=True)
        return
    
    await interaction.response.defer(ephemeral=True)
    
    embed = discord.Embed(
        title="⚠️ Command Deprecated",
        description="This command has been moved to the new **Developer Panel**.\n\nPlease use `/developer` instead for access to all developer tools.",
        color=discord.Color.orange()
    )
    await interaction.followup.send(embed=embed, ephemeral=True)

@bot.tree.command(name="debug_contracts", description="[DEPRECATED] Use /developer panel instead")
async def debug_contracts(interaction: discord.Interaction):
    # Check if user is bot owner
    if interaction.user.id != 782671382685024279:
        await interaction.response.send_message("❌ This command is only available to the bot developer.", ephemeral=True)
        return
    
    embed = discord.Embed(
        title="⚠️ Command Deprecated",
        description="This command has been moved to the new **Developer Panel**.\n\nPlease use `/developer` instead for access to all developer tools.",
        color=discord.Color.orange()
    )
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="debug_db", description="[DEPRECATED] Use /developer panel instead")
@app_commands.describe()
async def debug_db(intx: discord.Interaction):
    # Check if user is bot owner
    if intx.user.id != 782671382685024279:
        await intx.response.send_message("❌ This command is only available to the bot developer.", ephemeral=True)
        return
    
    embed = discord.Embed(
        title="⚠️ Command Deprecated",
        description="This command has been moved to the new **Developer Panel**.\n\nPlease use `/developer` instead for access to all developer tools.",
        color=discord.Color.orange()
    )
    await intx.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="debug_teams", description="Debug command to list all registered teams")
async def debug_teams(interaction: discord.Interaction):
    """Debug command to show all registered teams for troubleshooting"""
    # Check if user is bot owner
    if interaction.user.id != 782671382685024279:
        await interaction.response.send_message("❌ This command is only available to the bot developer.", ephemeral=True)
        return
    
    embed = discord.Embed(
        title="⚠️ Command Deprecated",
        description="This command has been moved to the new **Developer Panel**.\n\nPlease use `/developer` instead for access to all developer tools.",
        color=discord.Color.orange()
    )
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="backup_data", description="[DEPRECATED] Use /developer panel instead")
@app_commands.describe(
    force="Force backup even if not configured for Supabase"
)
async def backup_data(interaction: discord.Interaction, force: bool = False):
    # Check if user is bot owner
    if interaction.user.id != 782671382685024279:
        await interaction.response.send_message("❌ This command is only available to the bot developer.", ephemeral=True)
        return
    
    embed = discord.Embed(
        title="⚠️ Command Deprecated",
        description="This command has been moved to the new **Developer Panel**.\n\nPlease use `/developer` instead for access to all developer tools.",
        color=discord.Color.orange()
    )
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="sync_data", description="[DEPRECATED] Use /developer panel instead")
@app_commands.describe(
    force="Force sync even if not configured for Supabase"
)
async def sync_data(interaction: discord.Interaction, force: bool = False):
    # Check if user is bot owner
    if interaction.user.id != 782671382685024279:
        await interaction.response.send_message("❌ This command is only available to the bot developer.", ephemeral=True)
        return
    
    embed = discord.Embed(
        title="⚠️ Command Deprecated",
        description="This command has been moved to the new **Developer Panel**.\n\nPlease use `/developer` instead for access to all developer tools.",
        color=discord.Color.orange()
    )
    await interaction.response.send_message(embed=embed, ephemeral=True)

# Clean Duplicates Command (now automatic)
@bot.tree.command(name="clean_duplicates", description="Remove duplicate team entries (Admin only)")
async def clean_duplicates(interaction: discord.Interaction):
    # Check permissions
    if not await has_bot_management_permission(interaction):
        await interaction.response.send_message(
            "You need server administrator permissions or the configured Bot Admin Role to use this command.",
            ephemeral=True
        )
        return
    
    await interaction.response.defer(ephemeral=True)
    
    try:
        # Get all teams for this guild
        guild_id = str(interaction.guild.id)
        result = bot.db.execute("""
            SELECT role_id, slot_id, team_name, COUNT(*) as count
            FROM league_teams
            WHERE guild_id = ?
            GROUP BY role_id, slot_id
            HAVING COUNT(*) > 1
        """, (guild_id,))
        
        duplicates = result if result else []
        
        if not duplicates:
            await interaction.followup.send("✅ No duplicate team entries found!", ephemeral=True)
            return
        
        # Process duplicates
        total_removed = 0
        for role_id, slot_id, team_name, count in duplicates:
            # Keep one entry, remove the rest
            bot.db.execute("""
                DELETE FROM league_teams 
                WHERE guild_id = ? AND role_id = ? AND slot_id = ?
                AND rowid NOT IN (
                    SELECT MIN(rowid) 
                    FROM league_teams 
                    WHERE guild_id = ? AND role_id = ? AND slot_id = ?
                )
            """, (guild_id, role_id, slot_id, guild_id, role_id, slot_id))
            
            total_removed += count - 1
        
        bot.db.commit()
        
        await interaction.followup.send(f"✅ Cleaned up {total_removed} duplicate team entries!", ephemeral=True)
    
    except Exception as e:
        print(f"Error in clean_duplicates: {e}")
        traceback.print_exc()
        await interaction.followup.send(f"❌ Error cleaning duplicates: {e}", ephemeral=True)
