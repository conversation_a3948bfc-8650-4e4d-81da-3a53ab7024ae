#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Test for potential database access issues in the gametime command

class MockBot:
    def __init__(self):
        self.db = MockDB()

class MockDB:
    def __init__(self):
        self.db_type = 'sqlite'
    
    def execute(self, query, params=None):
        print(f"✅ DB execute called: {query[:50]}...")
        return [('test_slot', 'Test Team', '🏆', 'slot123')]
    
    def commit(self):
        print("✅ DB commit called")

# Test the specific database operations used in gametime
try:
    bot = MockBot()
    
    # Test the operations that might fail
    result = bot.db.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE role_id = ?", ("123456789",))
    print(f"✅ Team query result: {result}")
    
    # Test the gametimes table operation
    bot.db.execute('''
        INSERT INTO gametimes (
            game_id, guild_id, slot_id, team1_role_id, team1_name, team1_emoji,
            team2_role_id, team2_name, team2_emoji, scheduled_time, timezone,
            status, channel_id, message_id, created_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('test_game_id', 123, 'slot1', '111', 'Team1', '🏆', '222', 'Team2', '⚽', 1234567890, 'EST', 'active', 456, 789, 1234567890, 101))
    bot.db.commit()
    
    # Test the update operations that might fail
    bot.db.execute("UPDATE gametimes SET referees = ?, streamer = ? WHERE game_id = ?", ('[]', 'null', 'test_game_id'))
    bot.db.commit()
    
    print("✅ All database operations work correctly!")
    
    # Test if the DatabaseAdapter has the right attributes
    print(f"✅ db_type attribute exists: {hasattr(bot.db, 'db_type')}")
    print(f"✅ execute method exists: {hasattr(bot.db, 'execute')}")
    print(f"✅ commit method exists: {hasattr(bot.db, 'commit')}")
    
    # This should NOT exist and would cause the error
    print(f"❌ db attribute exists (should be False): {hasattr(bot.db, 'db')}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
