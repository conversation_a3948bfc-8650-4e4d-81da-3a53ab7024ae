#!/usr/bin/env python3
"""
Unit tests for management functions in the Discord bot
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import discord
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the module to test
import importlib.util
spec = importlib.util.spec_from_file_location("bot_module", "import sqlite3.py")
bot_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(bot_module)

class TestGetSlotIdFromInput(unittest.TestCase):
    """Test the get_slot_id_from_input function"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a mock for bot.db
        self.original_db = bot_module.bot.db
        bot_module.bot.db = Mock()
        
        # Mock for safe_get_first_row
        self.original_safe_get_first_row = bot_module.safe_get_first_row
        bot_module.safe_get_first_row = Mock()
    
    def tearDown(self):
        """Tear down test fixtures"""
        # Restore original functions
        bot_module.bot.db = self.original_db
        bot_module.safe_get_first_row = self.original_safe_get_first_row
    
    def test_get_slot_id_from_input_none(self):
        """Test when slot_input is None"""
        result = bot_module.get_slot_id_from_input("123456789", None)
        self.assertIsNone(result)
        bot_module.bot.db.execute.assert_not_called()
    
    def test_get_slot_id_from_input_by_id(self):
        """Test getting slot by ID"""
        # Setup mocks
        mock_result = Mock()
        bot_module.bot.db.execute.return_value = mock_result
        bot_module.safe_get_first_row.return_value = ["slot1"]
        
        # Call function
        result = bot_module.get_slot_id_from_input("123456789", "slot1")
        
        # Verify
        self.assertEqual(result, "slot1")
        bot_module.bot.db.execute.assert_called_once()
        bot_module.safe_get_first_row.assert_called_once_with(mock_result)
    
    def test_get_slot_id_from_input_by_name(self):
        """Test getting slot by name when ID lookup fails"""
        # Setup mocks for first call (ID lookup)
        mock_result1 = Mock()
        mock_result2 = Mock()
        bot_module.bot.db.execute.side_effect = [mock_result1, mock_result2]
        bot_module.safe_get_first_row.side_effect = [None, ["slot1"]]
        
        # Call function
        result = bot_module.get_slot_id_from_input("123456789", "NFL")
        
        # Verify
        self.assertEqual(result, "slot1")
        self.assertEqual(bot_module.bot.db.execute.call_count, 2)
        self.assertEqual(bot_module.safe_get_first_row.call_count, 2)
    
    def test_get_slot_id_from_input_not_found(self):
        """Test when slot is not found by ID or name"""
        # Setup mocks
        mock_result1 = Mock()
        mock_result2 = Mock()
        bot_module.bot.db.execute.side_effect = [mock_result1, mock_result2]
        bot_module.safe_get_first_row.side_effect = [None, None]
        
        # Call function
        result = bot_module.get_slot_id_from_input("123456789", "nonexistent")
        
        # Verify
        self.assertIsNone(result)
        self.assertEqual(bot_module.bot.db.execute.call_count, 2)
        self.assertEqual(bot_module.safe_get_first_row.call_count, 2)

class TestLiveManagementUpdateView(unittest.TestCase):
    """Test the LiveManagementUpdateView class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.view = bot_module.LiveManagementUpdateView("123", "456", "slot1")
    
    def test_init(self):
        """Test initialization of the view"""
        self.assertEqual(self.view.guild_id, "123")
        self.assertEqual(self.view.channel_id, "456")
        self.assertEqual(self.view.slot_id, "slot1")
        self.assertIsNone(self.view.timeout)  # Should be a persistent view
    
    @patch('bot_module.update_management_embed')
    async def test_update_list_wrong_channel(self, mock_update):
        """Test update_list when called from wrong channel"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.guild.id = "999"  # Different from view.guild_id
        interaction.channel.id = "999"  # Different from view.channel_id
        button = Mock(spec=discord.ui.Button)
        
        # Call method
        await self.view.update_list(interaction, button)
        
        # Verify
        interaction.response.defer.assert_called_once()
        interaction.followup.send.assert_called_once()
        self.assertIn("original channel", interaction.followup.send.call_args[0][0])
        mock_update.assert_not_called()
    
    @patch('bot_module.update_management_embed')
    async def test_update_list_auto_update_enabled(self, mock_update):
        """Test update_list when auto-update is enabled"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.guild.id = 123
        interaction.channel.id = 456
        button = Mock(spec=discord.ui.Button)
        
        # Mock database query for auto_update
        mock_result = Mock()
        bot_module.bot.db.execute.return_value = mock_result
        bot_module.safe_get_first_row.return_value = ["yes"]  # auto_update = 'yes'
        
        # Call method
        await self.view.update_list(interaction, button)
        
        # Verify
        interaction.response.defer.assert_called_once()
        interaction.followup.send.assert_called_once()
        self.assertIn("auto-update enabled", interaction.followup.send.call_args[0][0])
        mock_update.assert_not_called()
    
    @patch('bot_module.update_management_embed')
    async def test_update_list_success(self, mock_update):
        """Test successful update_list execution"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.guild.id = 123
        interaction.channel.id = 456
        button = Mock(spec=discord.ui.Button)
        
        # Mock database query for auto_update
        mock_result = Mock()
        bot_module.bot.db.execute.return_value = mock_result
        bot_module.safe_get_first_row.return_value = ["no"]  # auto_update = 'no'
        
        # Call method
        await self.view.update_list(interaction, button)
        
        # Verify
        interaction.response.defer.assert_called_once()
        mock_update.assert_called_once_with(interaction.guild, interaction.channel, "slot1")
        interaction.followup.send.assert_called_once()
        self.assertIn("updated manually", interaction.followup.send.call_args[0][0])
    
    @patch('bot_module.update_management_embed')
    async def test_update_list_exception(self, mock_update):
        """Test update_list when an exception occurs"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.guild.id = 123
        interaction.channel.id = 456
        button = Mock(spec=discord.ui.Button)
        
        # Mock database query to raise exception
        bot_module.bot.db.execute.side_effect = Exception("Test error")
        
        # Call method
        await self.view.update_list(interaction, button)
        
        # Verify
        interaction.response.defer.assert_called_once()
        interaction.followup.send.assert_called_once()
        self.assertIn("Error updating list", interaction.followup.send.call_args[0][0])
        mock_update.assert_not_called()

class TestEnsureManagementTable(unittest.TestCase):
    """Test the ensure_management_table function"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a mock for bot.db
        self.original_db = bot_module.bot.db
        bot_module.bot.db = Mock()
    
    def tearDown(self):
        """Tear down test fixtures"""
        # Restore original function
        bot_module.bot.db = self.original_db
    
    def test_ensure_management_table(self):
        """Test that the function creates the table if it doesn't exist"""
        # Call function
        bot_module.ensure_management_table()
        
        # Verify
        bot_module.bot.db.execute.assert_called_once()
        self.assertIn("CREATE TABLE IF NOT EXISTS", bot_module.bot.db.execute.call_args[0][0])
        self.assertIn("live_management_lists", bot_module.bot.db.execute.call_args[0][0])
        bot_module.bot.db.commit.assert_called_once()

class TestGetLabels(unittest.TestCase):
    """Test the get_labels function"""
    
    def test_get_labels_college_both(self):
        """Test get_labels with college style and both display type"""
        fo_label, gm_label = bot_module.get_labels("college", "AD", "both")
        self.assertEqual(fo_label, "UP")
        self.assertEqual(gm_label, "AD")
    
    def test_get_labels_college_owners_only(self):
        """Test get_labels with college style and owners display type"""
        fo_label, gm_label = bot_module.get_labels("college", "AD", "owners")
        self.assertEqual(fo_label, "UP")
        self.assertEqual(gm_label, "")
    
    def test_get_labels_college_gms_only(self):
        """Test get_labels with college style and gms display type"""
        fo_label, gm_label = bot_module.get_labels("college", "AD", "gms")
        self.assertEqual(fo_label, "")
        self.assertEqual(gm_label, "AD")
    
    def test_get_labels_nfl_both(self):
        """Test get_labels with NFL style and both display type"""
        fo_label, gm_label = bot_module.get_labels("nfl", "AD", "both")
        self.assertEqual(fo_label, "FO")
        self.assertEqual(gm_label, "GM")
    
    def test_get_labels_nfl_owners_only(self):
        """Test get_labels with NFL style and owners display type"""
        fo_label, gm_label = bot_module.get_labels("nfl", "AD", "owners")
        self.assertEqual(fo_label, "FO")
        self.assertEqual(gm_label, "")
    
    def test_get_labels_nfl_gms_only(self):
        """Test get_labels with NFL style and gms display type"""
        fo_label, gm_label = bot_module.get_labels("nfl", "AD", "gms")
        self.assertEqual(fo_label, "")
        self.assertEqual(gm_label, "GM")

class TestSubstituteEmoji(unittest.TestCase):
    """Test the substitute_emoji function"""
    
    def test_substitute_emoji_none(self):
        """Test substitute_emoji with None emoji"""
        result = bot_module.substitute_emoji(None, "Team Name", Mock())
        self.assertEqual(result, "Team Name")
    
    def test_substitute_emoji_football(self):
        """Test substitute_emoji with football emoji"""
        for emoji in bot_module.FOOTBALL_EMOJIS:
            result = bot_module.substitute_emoji(emoji, "Team Name", Mock())
            self.assertEqual(result, "Team Name")
    
    def test_substitute_emoji_custom(self):
        """Test substitute_emoji with custom emoji"""
        # This test is incomplete as the function is incomplete in the code snippet
        # We would need to see the full implementation to test it properly
        pass

if __name__ == "__main__":
    unittest.main()