#!/usr/bin/env python3
"""
Unit tests for GameView class in the Discord bot
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import discord
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the module to test
import importlib.util
spec = importlib.util.spec_from_file_location("bot_module", "import sqlite3.py")
bot_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(bot_module)

class TestGameView(unittest.TestCase):
    """Test the GameView class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Get the GameView class from the module
        self.GameView = None
        for attr_name in dir(bot_module):
            attr = getattr(bot_module, attr_name)
            if isinstance(attr, type) and attr.__name__ == "GameView":
                self.GameView = attr
                break
        
        if not self.GameView:
            self.skipTest("GameView class not found in module")
        
        # Create an instance
        self.game_id = "game123"
        self.view = self.GameView(self.game_id)
    
    def test_init(self):
        """Test initialization of GameView"""
        self.assertEqual(self.view.game_id, self.game_id)
        self.assertEqual(self.view.referees, [])
        self.assertIsNone(self.view.streamer)
        self.assertFalse(self.view.cancelled)
    
    @patch('discord.ui.Button')
    async def test_referee_button_add(self, mock_button):
        """Test referee button when adding a referee"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.user.id = 12345
        button = mock_button
        
        # Call method
        await self.view.referee_button(interaction, button)
        
        # Verify
        self.assertIn(12345, self.view.referees)
        interaction.response.send_message.assert_called_once()
        self.assertIn("now refereeing", interaction.response.send_message.call_args[0][0])
    
    @patch('discord.ui.Button')
    async def test_referee_button_remove(self, mock_button):
        """Test referee button when removing a referee"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.user.id = 12345
        button = mock_button
        
        # Add referee first
        self.view.referees.append(12345)
        
        # Call method
        await self.view.referee_button(interaction, button)
        
        # Verify
        self.assertNotIn(12345, self.view.referees)
        interaction.response.send_message.assert_called_once()
        self.assertIn("no longer refereeing", interaction.response.send_message.call_args[0][0])
    
    @patch('discord.ui.Button')
    async def test_streamer_button_add(self, mock_button):
        """Test streamer button when adding a streamer"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.user.id = 12345
        button = mock_button
        
        # Call method
        await self.view.streamer_button(interaction, button)
        
        # Verify
        self.assertEqual(self.view.streamer, 12345)
        interaction.response.send_message.assert_called_once()
        self.assertIn("now streaming", interaction.response.send_message.call_args[0][0])
    
    @patch('discord.ui.Button')
    async def test_streamer_button_remove(self, mock_button):
        """Test streamer button when removing a streamer"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.user.id = 12345
        button = mock_button
        
        # Set streamer first
        self.view.streamer = 12345
        
        # Call method
        await self.view.streamer_button(interaction, button)
        
        # Verify
        self.assertIsNone(self.view.streamer)
        interaction.response.send_message.assert_called_once()
        self.assertIn("no longer streaming", interaction.response.send_message.call_args[0][0])
    
    @patch('discord.ui.Button')
    @patch('bot_module.get_management_permission')
    async def test_cancel_button_with_permission(self, mock_get_permission, mock_button):
        """Test cancel button with permission"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.user.roles = []
        button = mock_button
        mock_get_permission.return_value = True
        
        # Call method
        await self.view.cancel_button(interaction, button)
        
        # Verify
        self.assertTrue(self.view.cancelled)
        for child in self.view.children:
            self.assertTrue(child.disabled)
        interaction.message.edit.assert_called_once()
        interaction.response.send_message.assert_called_once()
        self.assertIn("Game has been cancelled", interaction.response.send_message.call_args[0][0])
    
    @patch('discord.ui.Button')
    @patch('bot_module.get_management_permission')
    async def test_cancel_button_without_permission(self, mock_get_permission, mock_button):
        """Test cancel button without permission"""
        # Setup mocks
        interaction = Mock(spec=discord.Interaction)
        interaction.user.roles = []
        button = mock_button
        mock_get_permission.return_value = False
        
        # Set global variables needed for the test
        global user_team_role, opponent_role
        user_team_role = Mock(spec=discord.Role)
        opponent_role = Mock(spec=discord.Role)
        
        # Call method
        await self.view.cancel_button(interaction, button)
        
        # Verify
        self.assertFalse(self.view.cancelled)
        interaction.response.send_message.assert_called_once()
        self.assertIn("don't have permission", interaction.response.send_message.call_args[0][0])
        interaction.message.edit.assert_not_called()

if __name__ == "__main__":
    unittest.main()