#!/usr/bin/env python3
"""
Supabase Database Migration Script
==================================

This script will:
1. Connect to your Supabase database
2. Delete all existing tables (if they exist)
3. Create all required tables with proper schema
4. Optionally migrate data from SQLite to Supabase

Usage:
    python supabase_migration.py

Make sure to update the SUPABASE_URL and SUPABASE_KEY variables below!
"""

import os
import sys
import sqlite3
from datetime import datetime

try:
    from supabase import create_client, Client
    print("✅ Supabase library found")
except ImportError:
    print("❌ Supabase library not found. Install with: pip install supabase")
    sys.exit(1)

# =========================
# CONFIGURATION
# =========================

# Your Supabase credentials (update these!)
SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"
SUPABASE_KEY = "sb_secret_V70Cds-a_3H5dnz7TEwvvw_ZwtEl2z7"

# SQLite database path (for migration)
SQLITE_DB_PATH = "transaction_bot.db"

# Migration options
MIGRATE_DATA = True  # Set to False if you only want to create tables
DELETE_EXISTING = True  # Set to False if you want to keep existing data

# =========================
# TABLE CREATION SCRIPTS
# =========================

CREATE_TABLES_SQL = """
-- Drop all existing tables first (if DELETE_EXISTING is True)
DROP TABLE IF EXISTS live_management_lists CASCADE;
DROP TABLE IF EXISTS disband_transactions CASCADE;
DROP TABLE IF EXISTS gametimes CASCADE;
DROP TABLE IF EXISTS slot_team_blacklist CASCADE;
DROP TABLE IF EXISTS slot_team_whitelist CASCADE;
DROP TABLE IF EXISTS slot_trade_config CASCADE;
DROP TABLE IF EXISTS slot_verification_config CASCADE;
DROP TABLE IF EXISTS slot_demand_config CASCADE;
DROP TABLE IF EXISTS slot_command_settings CASCADE;
DROP TABLE IF EXISTS applications CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS contracts CASCADE;
DROP TABLE IF EXISTS league_teams CASCADE;
DROP TABLE IF EXISTS slot_configs CASCADE;
DROP TABLE IF EXISTS slots CASCADE;
DROP TABLE IF EXISTS guild_settings CASCADE;

-- Create guild_settings table
CREATE TABLE guild_settings (
    guild_id TEXT PRIMARY KEY,
    admin_role TEXT,
    transaction_channel TEXT,
    log_channel TEXT,
    application_channel TEXT,
    suspended_role TEXT,
    suspended_channel TEXT,
    application_blacklist_role TEXT,
    announcement_channel TEXT,
    transaction_log_channel TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create slots table
CREATE TABLE slots (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_name TEXT NOT NULL,
    description TEXT,
    is_default INTEGER DEFAULT 0,
    weeks_per_season INTEGER DEFAULT 17,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id)
);

-- Create slot_configs table
CREATE TABLE slot_configs (
    slot_id TEXT PRIMARY KEY,
    franchise_owner_role TEXT,
    general_manager_role TEXT,
    head_coach_role TEXT,
    assistant_coach_role TEXT,
    free_agent_role TEXT,
    transaction_channel TEXT,
    gametime_channel TEXT,
    referee_role TEXT,
    streamer_role TEXT,
    score_channel TEXT,
    trade_channel TEXT,
    draft_channel TEXT,
    pickup_host_role TEXT,
    pickup_channel TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create league_teams table
CREATE TABLE league_teams (
    guild_id INTEGER NOT NULL,
    role_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team_name TEXT NOT NULL,
    emoji TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (guild_id, role_id, slot_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id) ON DELETE CASCADE
);

-- Create contracts table
CREATE TABLE contracts (
    contract_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    player_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    team_name TEXT,
    contract_amount INTEGER NOT NULL,
    contract_length INTEGER NOT NULL,
    contract_length_unit TEXT DEFAULT 'years',
    time_remaining INTEGER NOT NULL,
    time_remaining_unit TEXT DEFAULT 'years',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    signed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_date TIMESTAMP,
    status TEXT DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create transactions table
CREATE TABLE transactions (
    transaction_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT,
    transaction_type TEXT NOT NULL,
    description TEXT,
    performed_by TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    affected_users_count INTEGER DEFAULT 0,
    player_id TEXT,
    from_team_role TEXT,
    to_team_role TEXT,
    details TEXT,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create applications table
CREATE TABLE applications (
    application_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    application_type TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by TEXT,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create slot_command_settings table
CREATE TABLE slot_command_settings (
    slot_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    sign_enabled TEXT DEFAULT 'on',
    release_enabled TEXT DEFAULT 'on',
    offer_enabled TEXT DEFAULT 'on',
    roster_cap INTEGER DEFAULT 25,
    money_cap_enabled TEXT DEFAULT 'on',
    money_cap_amount INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create slot_demand_config table
CREATE TABLE slot_demand_config (
    slot_id TEXT PRIMARY KEY,
    demands_enabled BOOLEAN DEFAULT false,
    demand_limit INTEGER DEFAULT 3,
    demand_channel TEXT,
    five_demands_role TEXT,
    four_demands_role TEXT,
    three_demands_role TEXT,
    two_demands_role TEXT,
    one_demand_role TEXT,
    no_demands_role TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create slot_verification_config table
CREATE TABLE slot_verification_config (
    slot_id TEXT PRIMARY KEY,
    verification_enabled BOOLEAN DEFAULT false,
    verified_role TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create slot_trade_config table
CREATE TABLE slot_trade_config (
    slot_id TEXT PRIMARY KEY,
    trades_enabled BOOLEAN DEFAULT true,
    trade_channel TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create slot_team_whitelist table
CREATE TABLE slot_team_whitelist (
    id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(slot_id, team_role_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create slot_team_blacklist table
CREATE TABLE slot_team_blacklist (
    id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(slot_id, team_role_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create gametimes table
CREATE TABLE gametimes (
    game_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team1_role_id TEXT NOT NULL,
    team1_name TEXT NOT NULL,
    team1_emoji TEXT,
    team2_role_id TEXT NOT NULL,
    team2_name TEXT NOT NULL,
    team2_emoji TEXT,
    scheduled_time BIGINT NOT NULL,
    timezone TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    channel_id TEXT NOT NULL,
    message_id TEXT NOT NULL,
    created_at BIGINT NOT NULL,
    created_by TEXT NOT NULL,
    referees TEXT DEFAULT '[]',
    streamer TEXT DEFAULT 'null',
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create disband_transactions table
CREATE TABLE disband_transactions (
    disband_id TEXT PRIMARY KEY,
    guild_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    team_role_id TEXT NOT NULL,
    team_name TEXT NOT NULL,
    disbanded_by TEXT NOT NULL,
    disbanded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    moved_to_fa_role TEXT,
    affected_users TEXT,
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create live_management_lists table
CREATE TABLE live_management_lists (
    guild_id TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    slot_id TEXT NOT NULL,
    display_type TEXT NOT NULL,
    auto_update TEXT DEFAULT 'yes',
    include_empty TEXT DEFAULT 'yes',
    league_style TEXT DEFAULT 'nfl',
    secondary_label TEXT DEFAULT 'AD',
    message_ids TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    active TEXT DEFAULT 'yes',
    PRIMARY KEY (guild_id, channel_id, slot_id),
    FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id),
    FOREIGN KEY (slot_id) REFERENCES slots(slot_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_slots_guild_id ON slots(guild_id);
CREATE INDEX IF NOT EXISTS idx_league_teams_guild_slot ON league_teams(guild_id, slot_id);
CREATE INDEX IF NOT EXISTS idx_contracts_guild_slot ON contracts(guild_id, slot_id);
CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status);
CREATE INDEX IF NOT EXISTS idx_transactions_guild_slot ON transactions(guild_id, slot_id);
CREATE INDEX IF NOT EXISTS idx_gametimes_guild_slot ON gametimes(guild_id, slot_id);
CREATE INDEX IF NOT EXISTS idx_gametimes_status ON gametimes(status);
"""

# =========================
# MIGRATION FUNCTIONS
# =========================

class SupabaseMigrator:
    def __init__(self, supabase_url: str, supabase_key: str):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.client = None
        
    def connect(self):
        """Connect to Supabase"""
        try:
            print(f"🔗 Connecting to Supabase...")
            print(f"   URL: {self.supabase_url}")
            print(f"   Key: {self.supabase_key[:20]}...{self.supabase_key[-10:]}")
            
            self.client = create_client(self.supabase_url, self.supabase_key)
            
            # Test connection
            test_result = self.client.rpc('version').execute()
            print(f"✅ Connected to Supabase successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect to Supabase: {e}")
            return False
    
    def execute_sql(self, sql: str):
        """Execute raw SQL on Supabase"""
        try:
            # Split SQL into individual statements
            statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]
            
            executed_count = 0
            for statement in statements:
                if statement.upper().startswith(('CREATE', 'DROP', 'ALTER', 'INSERT', 'UPDATE', 'DELETE')):
                    try:
                        # Use rpc to execute SQL
                        result = self.client.rpc('exec_sql', {'sql': statement}).execute()
                        executed_count += 1
                        print(f"✅ Executed: {statement[:50]}...")
                    except Exception as e:
                        print(f"⚠️ Error executing: {statement[:50]}...")
                        print(f"   Error: {e}")
                        # Continue with other statements
                        
            print(f"📊 Executed {executed_count} SQL statements")
            return True
            
        except Exception as e:
            print(f"❌ Failed to execute SQL: {e}")
            return False
    
    def create_tables(self):
        """Create all required tables"""
        print("🔨 Creating database tables...")
        
        if DELETE_EXISTING:
            print("🗑️ Deleting existing tables...")
            
        return self.execute_sql(CREATE_TABLES_SQL)
    
    def migrate_sqlite_data(self, sqlite_path: str):
        """Migrate data from SQLite to Supabase"""
        if not MIGRATE_DATA:
            print("⏭️ Skipping data migration (MIGRATE_DATA is False)")
            return True
            
        if not os.path.exists(sqlite_path):
            print(f"⚠️ SQLite database not found at {sqlite_path}")
            return True
            
        print(f"📦 Migrating data from SQLite: {sqlite_path}")
        
        try:
            # Connect to SQLite
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_cursor = sqlite_conn.cursor()
            
            # Get list of tables
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in sqlite_cursor.fetchall()]
            
            migrated_tables = 0
            total_records = 0
            
            for table_name in tables:
                if table_name.startswith('sqlite_'):
                    continue  # Skip SQLite system tables
                    
                try:
                    print(f"📋 Migrating table: {table_name}")
                    
                    # Get all data from SQLite table
                    sqlite_cursor.execute(f"SELECT * FROM {table_name}")
                    rows = sqlite_cursor.fetchall()
                    
                    if not rows:
                        print(f"   📭 No data in {table_name}")
                        continue
                    
                    # Get column names
                    sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in sqlite_cursor.fetchall()]
                    
                    # Convert rows to dictionaries
                    data = []
                    for row in rows:
                        record = {}
                        for i, value in enumerate(row):
                            if i < len(columns):
                                record[columns[i]] = value
                        data.append(record)
                    
                    # Insert into Supabase
                    if data:
                        result = self.client.table(table_name).insert(data).execute()
                        print(f"   ✅ Migrated {len(data)} records to {table_name}")
                        total_records += len(data)
                        migrated_tables += 1
                        
                except Exception as e:
                    print(f"   ❌ Error migrating {table_name}: {e}")
                    continue
            
            sqlite_conn.close()
            print(f"🎉 Migration complete: {migrated_tables} tables, {total_records} total records")
            return True
            
        except Exception as e:
            print(f"❌ Error during data migration: {e}")
            return False
    
    def verify_migration(self):
        """Verify that tables were created successfully"""
        print("🔍 Verifying migration...")
        
        try:
            # Test each main table
            test_tables = [
                'guild_settings', 'slots', 'slot_configs', 'league_teams',
                'contracts', 'transactions', 'applications', 'slot_command_settings',
                'slot_demand_config', 'slot_verification_config', 'slot_trade_config',
                'slot_team_whitelist', 'slot_team_blacklist', 'gametimes',
                'disband_transactions', 'live_management_lists'
            ]
            
            verified_tables = 0
            for table_name in test_tables:
                try:
                    result = self.client.table(table_name).select('*').limit(1).execute()
                    print(f"   ✅ {table_name}: OK")
                    verified_tables += 1
                except Exception as e:
                    print(f"   ❌ {table_name}: {e}")
            
            print(f"📊 Verified {verified_tables}/{len(test_tables)} tables")
            return verified_tables == len(test_tables)
            
        except Exception as e:
            print(f"❌ Error during verification: {e}")
            return False

# =========================
# MAIN EXECUTION
# =========================

def main():
    print("=" * 50)
    print("🚀 SUPABASE DATABASE MIGRATION")
    print("=" * 50)
    print(f"📅 Started at: {datetime.now()}")
    print(f"🗄️ Target: {SUPABASE_URL}")
    print(f"📦 Migrate data: {MIGRATE_DATA}")
    print(f"🗑️ Delete existing: {DELETE_EXISTING}")
    print()
    
    # Validate configuration
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Please update SUPABASE_URL and SUPABASE_KEY in this script!")
        return False
    
    # Create migrator
    migrator = SupabaseMigrator(SUPABASE_URL, SUPABASE_KEY)
    
    # Connect to Supabase
    if not migrator.connect():
        print("❌ Migration failed: Could not connect to Supabase")
        return False
    
    # Create tables
    print()
    if not migrator.create_tables():
        print("❌ Migration failed: Could not create tables")
        return False
    
    # Migrate data
    print()
    if not migrator.migrate_sqlite_data(SQLITE_DB_PATH):
        print("⚠️ Data migration had issues, but continuing...")
    
    # Verify migration
    print()
    if not migrator.verify_migration():
        print("⚠️ Some tables could not be verified")
    
    print()
    print("=" * 50)
    print("🎉 MIGRATION COMPLETED!")
    print("=" * 50)
    print("Your Supabase database is now ready to use.")
    print("Update your bot's DATABASE_TYPE to 'supabase' and restart.")
    print()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("✅ Migration completed successfully!")
            sys.exit(0)
        else:
            print("❌ Migration failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ Migration cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
