#!/usr/bin/env python3
"""
Test script to verify database migration works correctly for transactions table.
This simulates what happens when the bot starts up on your hosting platform.
"""

import sqlite3
import os

def test_migration():
    """Test the migration process for the transactions table"""
    
    # Create a test database with old schema (missing performed_by column)
    test_db_path = 'test_migration.db'
    
    # Remove test db if it exists
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    print("Creating test database with old transactions schema...")
    conn = sqlite3.connect(test_db_path)
    cursor = conn.cursor()
    
    # Create old transactions table (missing performed_by column)
    cursor.execute('''
    CREATE TABLE transactions (
        transaction_id TEXT PRIMARY KEY,
        guild_id TEXT NOT NULL,
        transaction_type TEXT NOT NULL,
        description TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )''')
    
    # Insert some test data
    cursor.execute('''
    INSERT INTO transactions (transaction_id, guild_id, transaction_type, description)
    VALUES ('test1', '123456789', 'team_swap', 'Test swap transaction')
    ''')
    
    conn.commit()
    print("✅ Created old schema with test data")
    
    # Now simulate the migration process
    print("\nStarting migration process...")
    
    # Check current schema
    cursor.execute("PRAGMA table_info(transactions)")
    old_columns = [column[1] for column in cursor.fetchall()]
    print(f"Old columns: {old_columns}")
    
    # Migration logic (same as in the bot)
    columns_to_add = {
        'slot_id': 'TEXT',
        'performed_by': 'TEXT NOT NULL',
        'affected_users_count': 'INTEGER DEFAULT 0',
        'player_id': 'TEXT',
        'from_team_role': 'TEXT',
        'to_team_role': 'TEXT',
        'details': 'TEXT'
    }
    
    for col_name, col_type in columns_to_add.items():
        if col_name not in old_columns:
            print(f"Adding column: {col_name}")
            try:
                if col_name == 'performed_by':
                    # For NOT NULL columns, we need to provide a default value for existing rows
                    cursor.execute(f"ALTER TABLE transactions ADD COLUMN {col_name} TEXT DEFAULT 'unknown'")
                    cursor.execute(f"UPDATE transactions SET {col_name} = 'unknown' WHERE {col_name} IS NULL")
                else:
                    cursor.execute(f"ALTER TABLE transactions ADD COLUMN {col_name} {col_type}")
                print(f"✅ Successfully added {col_name}")
            except sqlite3.OperationalError as e:
                print(f"❌ Could not add column {col_name}: {e}")
                return False
    
    # Update migrated rows with proper defaults
    cursor.execute("UPDATE transactions SET slot_id = 'migrated_default' WHERE slot_id IS NULL")
    
    conn.commit()
    print("\n✅ Migration completed successfully!")
    
    # Verify the new schema
    cursor.execute("PRAGMA table_info(transactions)")
    new_columns = [column[1] for column in cursor.fetchall()]
    print(f"New columns: {new_columns}")
    
    # Test inserting data with new schema
    print("\nTesting insert with new schema...")
    try:
        cursor.execute('''
        INSERT INTO transactions (transaction_id, guild_id, transaction_type, description,
                                performed_by, timestamp, affected_users_count)
        VALUES ('test2', '123456789', 'team_swap', 'Test swap after migration', 
                '987654321', datetime('now'), 2)
        ''')
        conn.commit()
        print("✅ Insert with new schema successful!")
        
        # Verify data
        cursor.execute("SELECT * FROM transactions")
        rows = cursor.fetchall()
        print(f"Total rows: {len(rows)}")
        for row in rows:
            print(f"  Row: {row}")
            
    except sqlite3.OperationalError as e:
        print(f"❌ Insert failed: {e}")
        return False
    
    conn.close()
    
    # Clean up
    os.remove(test_db_path)
    print("\n🎉 Migration test completed successfully!")
    return True

if __name__ == "__main__":
    success = test_migration()
    if success:
        print("\n✅ The migration code should fix your hosting platform database!")
        print("When you restart your bot, it will automatically add the missing columns.")
    else:
        print("\n❌ Migration test failed - there may be an issue with the migration code.")
