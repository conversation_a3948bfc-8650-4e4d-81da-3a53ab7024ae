# =========================
# RosterFlow Discord Bot - Complete Working Version
# League Management Bot for Discord Servers
# =========================

import re
from typing import Optional, Tuple, Dict, Any, List, Literal
from dataclasses import dataclass
from datetime import datetime, timedelta
import threading
import logging
import json
import asyncio
import discord
from discord import app_commands
from discord.ext import commands

# Discord snowflake ID pattern (17-19 digits) - Fixed regex pattern
ROLE_ID_PATTERN = re.compile(r'^\d{17,19}$')

@dataclass
class RoleValidationResult:
    """Result of role validation with detailed information"""
    role: Optional[discord.Role] = None
    role_id: str = ""
    is_valid: bool = False
    display_name: str = ""
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []

@dataclass
class ValidationError:
    """Represents a single validation error with context"""
    error_type: str
    item_type: str  # 'role' or 'channel'
    item_id: str
    item_name: Optional[str] = None
    context: Optional[str] = None
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []

# Bot configuration
DISCORD_BOT_TOKEN = ""  # Add your bot token here
DATABASE_TYPE_SETTING = 'sqlite'  # 'sqlite' or 'supabase'
SUPABASE_URL_SETTING = ""  # Add your Supabase URL if using Supabase
SUPABASE_KEY_SETTING = ""  # Add your Supabase key if using Supabase

# Continue with the rest of the bot code...
