#!/usr/bin/env python3
"""
Comprehensive Test Suite for RosterFlow Discord Bot
Tests for syntax errors, import issues, database functions, and command validation
"""

import ast
import sys
import sqlite3
import re
import importlib.util
from typing import List, Dict, Any
import traceback

class BotTestSuite:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.test_results = {}
        
    def log_error(self, test_name: str, error: str):
        self.errors.append(f"❌ {test_name}: {error}")
        
    def log_warning(self, test_name: str, warning: str):
        self.warnings.append(f"⚠️ {test_name}: {warning}")
        
    def log_success(self, test_name: str):
        self.test_results[test_name] = "✅ PASSED"

    def test_syntax_validation(self, file_path: str):
        """Test Python syntax validation"""
        print("🔍 Testing Python syntax...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Parse the AST to check for syntax errors
            ast.parse(source_code)
            self.log_success("Syntax Validation")
            
        except SyntaxError as e:
            self.log_error("Syntax Validation", f"Syntax error at line {e.lineno}: {e.msg}")
        except Exception as e:
            self.log_error("Syntax Validation", f"Unexpected error: {str(e)}")

    def test_import_validation(self, file_path: str):
        """Test if all imports can be resolved (excluding discord.py for now)"""
        print("🔍 Testing import validation...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Find all import statements
            tree = ast.parse(source_code)
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            # Test standard library imports
            standard_libs = ['sqlite3', 'json', 'datetime', 'asyncio', 'logging', 
                           'os', 're', 'sys', 'traceback', 'uuid', 'random']
            
            missing_std_libs = []
            for lib in standard_libs:
                if lib in imports:
                    try:
                        importlib.import_module(lib)
                    except ImportError:
                        missing_std_libs.append(lib)
            
            if missing_std_libs:
                self.log_error("Import Validation", f"Missing standard libraries: {missing_std_libs}")
            else:
                self.log_success("Standard Library Imports")
                
            # Check for discord.py dependency (warn if not available but don't fail)
            if any('discord' in imp for imp in imports):
                try:
                    importlib.import_module('discord')
                    self.log_success("Discord.py Import")
                except ImportError:
                    self.log_warning("Discord.py Import", "discord.py not installed (expected for testing)")
            
        except Exception as e:
            self.log_error("Import Validation", f"Error checking imports: {str(e)}")

    def test_database_functions(self, file_path: str):
        """Test database-related functions and SQL syntax"""
        print("🔍 Testing database functions...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Find SQL queries in the code
            sql_patterns = [
                r'CREATE TABLE[^"]*?"([^"]*)"',
                r'INSERT INTO[^"]*?"([^"]*)"',
                r'SELECT[^"]*?FROM[^"]*?"([^"]*)"',
                r'UPDATE[^"]*?"([^"]*)"',
                r'DELETE FROM[^"]*?"([^"]*)"',
                r'ALTER TABLE[^"]*?"([^"]*)"'
            ]
            
            sql_queries = []
            for pattern in sql_patterns:
                matches = re.findall(pattern, source_code, re.IGNORECASE | re.DOTALL)
                sql_queries.extend(matches)
            
            # Test database schema creation
            test_db = sqlite3.connect(':memory:')
            cursor = test_db.cursor()
            
            # Extract CREATE TABLE statements
            create_statements = re.findall(
                r'CREATE TABLE[^"]*?(?:IF NOT EXISTS\s+)?([^"(]+)\s*\([^)]*\)',
                source_code, 
                re.IGNORECASE | re.DOTALL
            )
            
            tables_created = 0
            for statement in create_statements:
                try:
                    # This is a simplified test - we're not executing the full SQL
                    # Just checking if table names are valid
                    table_name = statement.strip()
                    if table_name and not table_name.startswith('"""'):
                        tables_created += 1
                except Exception as e:
                    self.log_warning("Database Schema", f"Potential issue with table: {statement}")
            
            test_db.close()
            
            if tables_created > 0:
                self.log_success(f"Database Schema ({tables_created} tables found)")
            else:
                self.log_warning("Database Schema", "No CREATE TABLE statements found")
                
        except Exception as e:
            self.log_error("Database Functions", f"Error testing database: {str(e)}")

    def test_command_structure(self, file_path: str):
        """Test Discord command structure and decorators"""
        print("🔍 Testing command structure...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Find bot commands
            command_patterns = [
                r'@bot\.tree\.command\(name="([^"]*)"',
                r'@app_commands\.command\(name="([^"]*)"'
            ]
            
            commands_found = []
            for pattern in command_patterns:
                matches = re.findall(pattern, source_code)
                commands_found.extend(matches)
            
            # Check for common command issues
            tree = ast.parse(source_code)
            
            async_functions = []
            for node in ast.walk(tree):
                if isinstance(node, ast.AsyncFunctionDef):
                    async_functions.append(node.name)
            
            if len(commands_found) > 0:
                self.log_success(f"Command Structure ({len(commands_found)} commands found)")
                
                # List all commands found
                print(f"📋 Commands detected: {', '.join(commands_found)}")
                
                # Check if command functions are async
                command_functions_async = sum(1 for cmd in commands_found if cmd in async_functions)
                if command_functions_async == len(commands_found):
                    self.log_success("Async Command Functions")
                else:
                    self.log_warning("Async Command Functions", 
                                   f"Some commands may not be async ({command_functions_async}/{len(commands_found)})")
            else:
                self.log_warning("Command Structure", "No bot commands found")
                
        except Exception as e:
            self.log_error("Command Structure", f"Error testing commands: {str(e)}")

    def test_function_definitions(self, file_path: str):
        """Test for undefined functions and potential issues"""
        print("🔍 Testing function definitions...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            tree = ast.parse(source_code)
            
            # Find all function definitions
            function_defs = set()
            class_methods = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                    # Check if it's a method inside a class
                    for parent in ast.walk(tree):
                        if isinstance(parent, ast.ClassDef):
                            for child in ast.walk(parent):
                                if child == node:
                                    class_methods.add(f"{parent.name}.{node.name}")
                                    break
                    function_defs.add(node.name)
            
            # Find function calls
            function_calls = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
                    function_calls.add(node.func.id)
                elif isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
                    if hasattr(node.func, 'attr'):
                        function_calls.add(node.func.attr)
            
            # Check for potentially undefined functions (excluding built-ins and external)
            builtin_functions = {'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple', 'range', 'enumerate', 'zip', 'any', 'all', 'sum', 'min', 'max', 'sorted', 'reversed', 'open', 'type', 'isinstance', 'hasattr', 'getattr', 'setattr'}
            external_functions = {'create_client', 'connect', 'execute', 'fetchall', 'fetchone', 'commit', 'close', 'add_view', 'change_presence', 'send', 'respond', 'followup'}
            
            undefined_calls = function_calls - function_defs - builtin_functions - external_functions
            
            if len(undefined_calls) > 10:  # Many will be external library calls
                self.log_warning("Function Definitions", f"Many external function calls detected ({len(undefined_calls)})")
            else:
                self.log_success("Function Definitions")
                
            self.log_success(f"Function Analysis ({len(function_defs)} functions, {len(class_methods)} methods)")
            
        except Exception as e:
            self.log_error("Function Definitions", f"Error analyzing functions: {str(e)}")

    def test_variable_usage(self, file_path: str):
        """Test for potential variable issues"""
        print("🔍 Testing variable usage...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Check for common variable patterns that might cause issues
            potential_issues = []
            
            # Check for bot token exposure (should use environment variables)
            if re.search(r'bot\.run\s*\(\s*[\'"][A-Za-z0-9._-]{50,}[\'"]', source_code):
                potential_issues.append("Hardcoded bot token detected")
            
            # Check for database file references
            if 'transaction_bot.db' in source_code:
                self.log_success("Database File References")
            
            # Check for proper error handling patterns
            try_except_count = len(re.findall(r'try\s*:', source_code))
            if try_except_count > 5:
                self.log_success(f"Error Handling ({try_except_count} try blocks)")
            else:
                self.log_warning("Error Handling", "Limited error handling detected")
            
            if potential_issues:
                for issue in potential_issues:
                    self.log_warning("Variable Usage", issue)
            else:
                self.log_success("Variable Usage")
                
        except Exception as e:
            self.log_error("Variable Usage", f"Error testing variables: {str(e)}")

    def test_discord_integration_patterns(self, file_path: str):
        """Test Discord.py integration patterns"""
        print("🔍 Testing Discord integration patterns...")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Check for essential Discord patterns
            patterns = {
                'Bot Instance': r'bot\s*=.*Bot\(',
                'Event Handlers': r'@bot\.event',
                'Slash Commands': r'@bot\.tree\.command',
                'Interaction Response': r'interaction\.response',
                'Embed Creation': r'discord\.Embed',
                'UI Views': r'discord\.ui\.View',
                'App Commands': r'from discord import app_commands'
            }
            
            found_patterns = {}
            for pattern_name, pattern in patterns.items():
                matches = len(re.findall(pattern, source_code))
                found_patterns[pattern_name] = matches
                
                if matches > 0:
                    self.log_success(f"{pattern_name} ({matches} instances)")
                else:
                    self.log_warning(f"{pattern_name}", "Pattern not found")
            
            # Check for proper async/await usage
            await_count = len(re.findall(r'\bawait\s+', source_code))
            async_count = len(re.findall(r'\basync\s+def', source_code))
            
            if await_count > 0 and async_count > 0:
                self.log_success(f"Async/Await Usage ({async_count} async functions, {await_count} await calls)")
            else:
                self.log_warning("Async/Await Usage", "Limited async patterns detected")
                
        except Exception as e:
            self.log_error("Discord Integration", f"Error testing Discord patterns: {str(e)}")

    def run_all_tests(self, file_path: str):
        """Run all test suites"""
        print("🚀 Starting Comprehensive Bot Testing Suite")
        print("=" * 60)
        
        self.test_syntax_validation(file_path)
        self.test_import_validation(file_path)
        self.test_database_functions(file_path)
        self.test_command_structure(file_path)
        self.test_function_definitions(file_path)
        self.test_variable_usage(file_path)
        self.test_discord_integration_patterns(file_path)
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        # Print successes
        if self.test_results:
            print("\n✅ PASSED TESTS:")
            for test_name, result in self.test_results.items():
                print(f"   {result} {test_name}")
        
        # Print warnings
        if self.warnings:
            print(f"\n⚠️ WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   {warning}")
        
        # Print errors
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   {error}")
        else:
            print("\n🎉 NO CRITICAL ERRORS FOUND!")
        
        # Overall assessment
        print("\n" + "=" * 60)
        error_count = len(self.errors)
        warning_count = len(self.warnings)
        success_count = len(self.test_results)
        
        if error_count == 0:
            if warning_count == 0:
                print("🟢 DEPLOYMENT READY: All tests passed with no issues!")
            elif warning_count <= 3:
                print("🟡 DEPLOYMENT READY: Minor warnings detected but safe to deploy")
            else:
                print(f"🟡 REVIEW RECOMMENDED: {warning_count} warnings should be reviewed")
        else:
            print(f"🔴 DEPLOYMENT NOT RECOMMENDED: {error_count} critical errors must be fixed")
        
        print(f"\nTest Summary: {success_count} passed, {warning_count} warnings, {error_count} errors")
        return error_count == 0

if __name__ == "__main__":
    bot_file = "import sqlite3.py"
    
    if len(sys.argv) > 1:
        bot_file = sys.argv[1]
    
    tester = BotTestSuite()
    is_ready = tester.run_all_tests(bot_file)
    
    if is_ready:
        print("\n🚀 Your bot is ready for deployment to PebbleHost!")
    else:
        print("\n⚠️ Please fix the identified issues before deploying.")
    
    sys.exit(0 if is_ready else 1)
