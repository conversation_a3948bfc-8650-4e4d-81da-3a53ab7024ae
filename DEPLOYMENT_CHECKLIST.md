
# 🚀 RosterFlow Bot Deployment Checklist for PebbleHost

## Pre-Deployment Requirements

### 1. Dependencies Installation
```bash
pip install discord.py
pip install supabase  # Optional, for cloud database
```

### 2. Environment Setup
- [ ] Upload `import sqlite3.py` to your server
- [ ] Set bot token as environment variable: `BOT_TOKEN=your_token_here`
- [ ] Ensure SQLite3 is available (usually pre-installed on most servers)

### 3. Database Configuration
- [ ] Database file will be created automatically as `transaction_bot.db`
- [ ] Ensure write permissions for the bot directory
- [ ] For Supabase (optional): Set `SUPABASE_URL` and `SUPABASE_KEY` environment variables

### 4. Discord Application Setup
- [ ] Bo<PERSON> has proper permissions in Discord Developer Portal
- [ ] Bot token is secured and not hardcoded
- [ ] Required intents are enabled (Message Content Intent if needed)

## Deployment Steps

### 1. File Upload
- Upload `import sqlite3.py` to your PebbleHost server
- Rename the file to something like `rosterflow_bot.py` (optional)

### 2. Start Command
```bash
python3 import_sqlite3.py
# or if renamed:
python3 rosterflow_bot.py
```

### 3. Initial Testing
- [ ] Test with `/help` command
- [ ] Run `/global_setup` to configure basic settings
- [ ] Test with a simple command like `/ping`

## Post-Deployment Verification

### Critical Commands to Test
1. **Setup Commands**
   - [ ] `/global_setup` - Configure server-wide settings
   - [ ] `/setup` - Configure slot-specific settings
   - [ ] `/slots` - Slot management panel

2. **Team Management**
   - [ ] `/detect_teams` - Auto-detect teams from roles
   - [ ] `/add_team` - Add a new team
   - [ ] `/list_teams` - View all teams

3. **Player Management**
   - [ ] `/sign` - Sign a player
   - [ ] `/release` - Release a player
   - [ ] `/roster` - View team roster

4. **Information Commands**
   - [ ] `/help` - View command help
   - [ ] `/contracts` - View contracts

## Common Issues and Solutions

### Issue: "Bot is not responding"
- Check bot token is correct
- Verify bot has proper permissions in Discord server
- Check server logs for error messages

### Issue: "Database errors"
- Ensure write permissions for database file
- Check if SQLite3 is available: `python3 -c "import sqlite3; print('SQLite OK')"`

### Issue: "Command not found"
- Wait a few minutes for slash commands to sync
- Try running `/help` to see available commands
- Restart the bot if commands don't appear

### Issue: "Permission errors"
- Check bot role hierarchy in Discord server
- Ensure bot has "Manage Roles" permission
- Verify specific channel permissions

## Performance Optimization

### For Large Servers (1000+ members)
- Consider using Supabase for better performance
- Monitor database file size
- Set up regular backups using `/backup_data`

### Memory Management
- Bot uses approximately 50-100MB RAM under normal load
- Monitor for memory leaks if running continuously

## Backup and Maintenance

### Regular Backups
- Database file: `transaction_bot.db`
- Use `/developer` panel for automated backups
- Manual backup: Copy `transaction_bot.db` file

### Updates
- Always backup before updating bot code
- Test updates on a development server first
- Use `/developer reset` for emergency resets (destructive!)

## Support and Troubleshooting

### Log Monitoring
- Check console output for error messages
- Common errors are logged with clear descriptions
- Use `/developer` panel for debugging tools

### Emergency Commands
- `/guild_reset` - Complete server reset (IRREVERSIBLE)
- `/developer reset` - Bot owner only emergency reset

## Security Notes

- Never share your bot token
- Use environment variables for sensitive data
- Regularly update dependencies
- Monitor unusual bot activity

---

✅ **Your RosterFlow bot is ready for deployment!**

After following this checklist, your bot should be fully operational on PebbleHost.
For support, refer to the command help system using `/help`.
